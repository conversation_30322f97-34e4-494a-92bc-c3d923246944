'use client';

import { useState, useCallback } from 'react';
import { useSupabase } from '@/lib/supabase/provider';
import { useVisitorData } from '@fingerprintjs/fingerprintjs-pro-react';
import { useFingerprintJSContext } from '@/components/providers/fpjs-provider';


// Define a custom error type for fetch errors
export class HttpError extends Error {
  status: number;
  constructor(message: string, status: number) {
    super(message);
    this.name = 'HttpError';
    this.status = status;
  }
}

// Define the hook's return type
interface UseAuthenticatedFetchResult {
  authedFetch: <T = unknown>(
    url: string | URL | Request,
    options?: RequestInit
  ) => Promise<T>;
  isReady: boolean; // Indicates if both token and fingerprint are likely available
  error: string | null; // Stores errors from getting token/fingerprint
}

export function useAuthenticatedFetch(): UseAuthenticatedFetchResult {
  const { supabase } = useSupabase();
  const { isAvailable: isFingerprintJSAvailable } = useFingerprintJSContext();

  // Only use visitor data if FingerprintJS is available
  const {
    data: visitorData,
    isLoading: isLoadingVisitorData,
    error: visitorDataError,
  } = useVisitorData(
    { extendedResult: true },
    { immediate: isFingerprintJSAvailable }
  );

  const [error, setError] = useState<string | null>(null);

  // Determine if the hook is ready to make calls
  // If FingerprintJS is not available, we're ready as soon as Supabase is ready
  // If FingerprintJS is available, wait for it to load
  const isReady = !!supabase && (
    !isFingerprintJSAvailable || (!isLoadingVisitorData && !visitorDataError)
  );

  // The core authenticated fetch function
  const authedFetch = useCallback(async <T = unknown>(
    url: string | URL | Request,
    options: RequestInit = {}
  ): Promise<T> => {
    setError(null);

    // 1. Get Supabase Access Token
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) {
      const errorMessage = `Session error: ${sessionError.message}`;
      setError(errorMessage);
      console.error(errorMessage);
      throw new Error(errorMessage);
    }

    // Handle session synchronization issue: client session null but server may have valid session
    let token: string | undefined;
    if (!session?.access_token) {
      console.warn('Client session is null, attempting request with cookies only (server-side auth)');
      // Don't fail fast - let the request proceed with cookies, server will validate
      token = undefined;
    } else {
      token = session.access_token;
    }

    // 2. Get FingerprintJS Visitor ID
    const visitorId = visitorData?.visitorId;
    if (!visitorId) {
        // Proceed without fingerprint header when unavailable (no console noise in dev)
        if (process.env.NODE_ENV === 'production' && !isLoadingVisitorData && !visitorDataError) {
          // Optionally log at info level in production if needed for diagnostics
          // console.info('Fingerprint unavailable; proceeding without fingerprint header.');
        }
    }

    // 3. Prepare headers with token and potentially other security measures
    const requestHeaders: HeadersInit = {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      // Only add Authorization header if we have a token
      ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
      // Only add the device ID if available
      ...(visitorId ? { 'X-Device-ID': visitorId } : {}),
    };

    // Ensure Content-Type is set for POST/PUT etc. if body exists
    if (options.body && !requestHeaders['Content-Type']) {
      requestHeaders['Content-Type'] = 'application/json';
    }

    // 4. Make the fetch call
    try {
      const response = await fetch(url, {
        ...options,
        headers: requestHeaders,
        credentials: 'same-origin', // Ensure cookies are sent for auth
      });

      // Check for HTTP errors
      if (!response.ok) {
        let errorPayload: unknown = null;
        try {
            errorPayload = await response.json(); // Try to parse error body
        } catch { /* Ignore if body isn't JSON */ }
        const errorMessage = `HTTP error ${response.status}: ${response.statusText}. ${errorPayload ? JSON.stringify(errorPayload) : ''}`;
        const httpError = new HttpError(errorMessage, response.status); // Create error object first
        console.error('HTTP Error:', httpError, { url: typeof url === 'string' ? url : url.toString() }); // Log error object and URL
        throw httpError; // Throw the created error
      }

      // Attempt to parse JSON response, handle empty responses
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        // Check for empty body before parsing
        const text = await response.text();
        return text ? (JSON.parse(text) as T) : ({} as T); // Cast parsed JSON to T
      } else {
        // Handle non-JSON responses if necessary, e.g., return blob, text, etc.
        // For now, assume JSON or empty. If not JSON, return raw response?
        // Or maybe just return the text content?
        return await response.text() as unknown as T; // Adjust based on expected non-JSON types
      }

    } catch (err: unknown) {
      console.error('Fetch failed:', err);
      setError(err instanceof Error ? err.message : String(err));
      // Re-throw the error so the caller can handle it
      throw err;
    }
  }, [supabase, visitorData, isLoadingVisitorData, visitorDataError]); // Dependencies

  return { authedFetch, isReady, error };
}
