// frontend/src/app/api/deadlines/validation/stats/route.ts
import { withAuth, UserRole } from '@/lib/auth/server-exports';
import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import type { AuthUser } from '@/lib/auth/server-exports';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/lib/supabase/database.types';

// GET /api/deadlines/validation/stats - Get validation statistics
export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal],
  async (
    _req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database>,
    context?: Record<string, unknown>
  ): Promise<Response> => {
  try {
    // Validate tenant context
    if (!user.tenantId) {
      console.log('[GET /api/deadlines/validation/stats] No tenant ID for user:', user.id);
      return NextResponse.json({
        counts: { pending: 0, validated: 0, rejected: 0 },
        percentages: { pending: 0, validated: 0, rejected: 0 },
        total: 0
      });
    }

    console.log('[GET /api/deadlines/validation/stats] Processing request for tenant:', user.tenantId);

    // Use explicit schema to avoid cross-schema resolution issues
    const base = supabase.schema('tenants');

    // Validate supabase client
    if (!base) {
      console.error('[GET /api/deadlines/validation/stats] Failed to create supabase client');
      return NextResponse.json({ error: 'Database connection failed' }, { status: 500 });
    }

    const pendingResult = await base
      .from('deadlines')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', user.tenantId)
      .eq('validation_status', 'pending');

    const validatedResult = await base
      .from('deadlines')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', user.tenantId)
      .eq('validation_status', 'validated');

    const rejectedResult = await base
      .from('deadlines')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', user.tenantId)
      .eq('validation_status', 'rejected');

    // Check for errors in any of the queries
    if (pendingResult.error || validatedResult.error || rejectedResult.error) {
      const error = pendingResult.error || validatedResult.error || rejectedResult.error;
      const useFallback = process.env.DEV_API_FALLBACKS === 'true' && process.env.NODE_ENV !== 'production';
      if (useFallback) {
        console.warn('[DEV_FALLBACK] deadlines/validation/stats route using fallback due to: DB error');
        return NextResponse.json({
          counts: { pending: 0, validated: 0, rejected: 0 },
          percentages: { pending: 0, validated: 0, rejected: 0 },
          total: 0,
          meta: { devFallback: true }
        });
      }
      console.error('[GET /api/deadlines/validation/stats] Database error:', {
        error: error?.message || 'Unknown database error',
        errorCode: error?.code,
        errorDetails: error?.details,
        errorHint: error?.hint,
        fullError: error,
        tenantId: user.tenantId,
        timestamp: new Date().toISOString()
      });
      return NextResponse.json({ error: 'Failed to fetch validation statistics' }, { status: 500 });
    }

    // Calculate total count
    const pendingCount = pendingResult.count || 0;
    const validatedCount = validatedResult.count || 0;
    const rejectedCount = rejectedResult.count || 0;
    const totalCount = pendingCount + validatedCount + rejectedCount;

    // Calculate percentages
    const pendingPercentage = totalCount > 0 ? Math.round((pendingCount / totalCount) * 100) : 0;
    const validatedPercentage = totalCount > 0 ? Math.round((validatedCount / totalCount) * 100) : 0;
    const rejectedPercentage = totalCount > 0 ? Math.round((rejectedCount / totalCount) * 100) : 0;

    // Return the response object
    return NextResponse.json({
      counts: {
        pending: pendingCount,
        validated: validatedCount,
        rejected: rejectedCount
      },
      percentages: {
        pending: pendingPercentage,
        validated: validatedPercentage,
        rejected: rejectedPercentage
      },
      total: totalCount
    });
  } catch (error: unknown) {
    const useFallback = process.env.DEV_API_FALLBACKS === 'true' && process.env.NODE_ENV !== 'production';
    if (useFallback) {
      console.warn('[DEV_FALLBACK] deadlines/validation/stats route using fallback due to: exception');
      return NextResponse.json({
        counts: { pending: 0, validated: 0, rejected: 0 },
        percentages: { pending: 0, validated: 0, rejected: 0 },
        total: 0,
        meta: { devFallback: true }
      });
    }
    console.error('[GET /api/deadlines/validation/stats] Unexpected error:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      tenantId: user?.tenantId,
      timestamp: new Date().toISOString()
    });
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});
