import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { withAuth, UserRole } from '@/lib/auth/server-exports';
import type { AuthUser } from '@/lib/auth/server-exports';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/lib/supabase/database.types';

console.log('🔍 [USER ACTIVITY ROUTE] File loaded at:', new Date().toISOString());

export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (
    _request: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database>
  ): Promise<Response> => {
    console.log('🔍 [USER ACTIVITY ROUTE] GET handler called for user:', user.email);
    
    // Get user's last activity time from database
    const { data: userData, error: userError } = await supabase
      .schema('tenants')
      .from('users')
      .select('last_activity_at, created_at')
      .eq('auth_user_id', user.id)
      .single();

    if (userError) {
      console.error('Error fetching user activity:', {
        error: userError,
        code: userError.code,
        details: userError.details,
        hint: userError.hint,
        message: userError.message,
        auth_user_id: user.id
      });
      
      return NextResponse.json({ error: 'Failed to fetch user activity' }, { status: 500 });
    }

    const lastActivity = userData?.last_activity_at || userData?.created_at;
    const now = new Date();
    const timeAwayMs = lastActivity ? now.getTime() - new Date(lastActivity).getTime() : 0;
    const timeAwayHours = timeAwayMs / (1000 * 60 * 60);

    return NextResponse.json({
      last_activity_at: lastActivity,
      time_away_hours: timeAwayHours,
      should_trigger_insights: timeAwayHours >= 1
    });
  }
);

export async function POST() {
  console.log('🔍 [USER ACTIVITY ROUTE] POST handler called');
  return NextResponse.json({ 
    message: 'Activity POST working', 
    timestamp: new Date().toISOString() 
  });
}