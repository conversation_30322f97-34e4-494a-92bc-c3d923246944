// frontend/src/middleware.ts
import { NextResponse, type NextRequest } from "next/server";
import { UserRole } from "@/lib/types/auth";
// SECURITY: Removed parseJwtPayload import - using secure server-side validation
import { getUnifiedSession, getUserRoles } from "@/lib/auth/getUnifiedSession";
import { isSuperAdminEmail } from "@/lib/auth/constants";

// Enhanced security imports
import {
  addSecurityHeaders,
  validateRequest,
  logSecurityEvent,
  detectSuspiciousActivity,
  SecurityEventType
} from "@/lib/middleware/security-utils";

// LOOP PREVENTION: Import request context system
import {
  shouldSkipMiddleware,
  createRequestContext,
  shouldBlockRequest
} from "@/lib/middleware/request-context";

// MONITORING: Import performance monitoring
import { middlewareMonitor } from "@/lib/monitoring/middleware-monitor";

// DEDUPLICATION: Import request deduplication
import {
  shouldDeduplicateRequest,
  isDevEnvironmentDuplicate
} from "@/lib/middleware/request-deduplication";

// SESSION CACHE: Import session caching
import {
  getCachedSession,
  setCachedSession,
  isCachingEnabled
} from "@/lib/middleware/session-cache";

// Define protected routes and their required roles (using the standardized UserRole type)
const protectedRoutes: Record<string, UserRole[]> = {
  "/admin": [UserRole.Partner], // Regular admin dashboard (only partners)
  "/dashboard": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ], // Staff dashboard
  "/client-portal": [UserRole.Client], // Client portal
  "/cases": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/matters": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/documents": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/settings": [UserRole.Admin], // Tenant settings (Tenant Admins only)
  "/billing": [UserRole.Partner], // SaaS Billing (Partners only)
  "/users": [UserRole.Admin], // Tenant user management (Tenant Admins only)
  "/calendar": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/tasks": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/clients": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/submit-case": [UserRole.Client],
  // Note: /superadmin is handled separately by JWT is_super_admin claim
};

export async function middleware(request: NextRequest): Promise<NextResponse> {
  // MONITORING: Start performance tracking
  const requestMetrics = middlewareMonitor.startRequest(
    crypto.randomUUID(),
    request
  );

  try {
    // LOOP PREVENTION: Early exit for internal requests and static files
    if (shouldSkipMiddleware(request)) {
      middlewareMonitor.skipRequest(requestMetrics.requestId, 'internal/static request');
      return NextResponse.next({ request });
    }

    // DEDUPLICATION: Check for duplicate concurrent requests EARLY
    const dedupCheck = await shouldDeduplicateRequest(request);
    if (dedupCheck.shouldDeduplicate) {
      middlewareMonitor.skipRequest(requestMetrics.requestId, 'deduplicated');
      // Return early without any logging or processing
      return NextResponse.next({ request });
    }

    // Only log dev environment duplicates if they're not being deduplicated
    const isDev = process.env.NODE_ENV === 'development';
    const isDevDuplicate = isDev && isDevEnvironmentDuplicate(request);
    
    // LOOP PREVENTION: Create request context for tracking
    const requestContext = createRequestContext(request, 'middleware');
    
    // Update monitoring with request depth
    requestMetrics.depth = requestContext.depth;
    
    // LOOP PREVENTION: Block excessive request depth
    if (shouldBlockRequest(requestContext)) {
      console.error('🚨 Middleware: Blocking request due to excessive depth:', {
        path: request.nextUrl.pathname,
        depth: requestContext.depth,
        requestId: requestContext.id
      });
      
      const response = NextResponse.json(
        { error: 'Request depth limit exceeded' },
        { status: 429 }
      );
      
      middlewareMonitor.endRequest(requestMetrics.requestId, 429, new Error('Request depth exceeded'));
      return response;
    }

  // Only log processing for the first request (not duplicates)
  const isImportantPath = request.nextUrl.pathname.startsWith('/dashboard') ||
                         request.nextUrl.pathname.startsWith('/login') ||
                         request.nextUrl.pathname.startsWith('/superadmin');

  if (isImportantPath && !isDevDuplicate) {
    console.log('🚀 Middleware processing:', {
      path: request.nextUrl.pathname,
      timestamp: new Date().toISOString(),
      requestId: requestContext.id,
      depth: requestContext.depth
    });
  }

  // Force recompilation to pick up getUnifiedSession changes - v3

  // Create response with enhanced security
  const response = NextResponse.next({ request });

  // SECURITY ENHANCEMENT 1: Add comprehensive security headers
  const secureResponse = addSecurityHeaders(response, request);

  // SECURITY ENHANCEMENT 2: Validate request for security threats
  const validation = validateRequest(request);
  if (!validation.isValid) {
    console.warn('🚨 SECURITY VIOLATION DETECTED:', {
      path: request.nextUrl.pathname,
      violations: validation.violations,
      riskLevel: validation.riskLevel,
      ip: request.headers.get('x-forwarded-for') ?? 'unknown'
    });

    // Log security event
    await logSecurityEvent(
      SecurityEventType.MALICIOUS_PAYLOAD,
      request,
      {
        violations: validation.violations,
        riskLevel: validation.riskLevel
      }
    );

    // Block high-risk requests
    if (validation.riskLevel === 'high') {
      return NextResponse.json(
        { error: 'Request blocked for security reasons' },
        { status: 403 }
      );
    }
  }

  // SECURITY ENHANCEMENT 3: Detect suspicious activity
  const clientId = request.headers.get('x-forwarded-for') ??
                   request.headers.get('x-real-ip') ??
                   'unknown';

  const suspiciousActivity = detectSuspiciousActivity(request, clientId);
  if (suspiciousActivity.shouldBlock) {
    console.warn('🚨 SUSPICIOUS ACTIVITY BLOCKED:', {
      clientId,
      reason: suspiciousActivity.reason,
      path: request.nextUrl.pathname
    });

    await logSecurityEvent(
      SecurityEventType.SUSPICIOUS_REQUEST,
      request,
      {
        clientId,
        reason: suspiciousActivity.reason,
        blocked: true
      }
    );

    return NextResponse.json(
      { error: 'Access temporarily restricted' },
      { status: 429 }
    );
  }

  // Use the unified session approach instead of direct Supabase calls in middleware
  let session = null;
  let user = null;
  let userRoles: string[] = [];
  
  // MONITORING: Track authentication timing
  const authStartTime = Date.now();
  
  // SESSION CACHE: Check for cached session first
  if (isCachingEnabled()) {
    const cached = getCachedSession(request);
    if (cached) {
      session = cached.session;
      user = cached.user;
      userRoles = cached.roles;
      
      if (!isDevDuplicate) {
        console.log('✨ Middleware: Using cached session for user:', user?.email);
      }
      
      // Update monitoring with cached session
      const authTime = Date.now() - authStartTime;
      middlewareMonitor.updateSessionStatus(requestMetrics.requestId, true, authTime);
    }
  }
  
  // If no cached session, fetch it
  if (!session) {
    try {
      // Create a cookies-like object for the unified session
      // Make sure it returns the correct format that Supabase expects
      const cookiesLike = {
        get(name: string) {
          const cookie = request.cookies.get(name);
          // Only log cookie operations if not a dev duplicate
          if (!isDevDuplicate) {
            console.log(`🍪 Middleware cookie get: ${name} = ${cookie ? 'found' : 'not found'}`);
            if (cookie) {
              console.log(`🍪 Middleware cookie value length: ${cookie.value?.length}`);
            }
          }
          // Return the cookie object directly - Supabase SSR expects this format
          return cookie ?? undefined;
        }
      };

      if (!isDevDuplicate) {
        console.log('🔍 Middleware: About to call getUnifiedSession...');
      }
      // Use the unified session approach which handles the Supabase complexity
      const unifiedSession = await getUnifiedSession(cookiesLike);
      if (!isDevDuplicate) {
        console.log('🔍 Middleware: getUnifiedSession completed');
      }

      if (unifiedSession) {
        session = unifiedSession;
        user = unifiedSession.user;
        
        // Get user roles for caching
        userRoles = getUserRoles(session);

        if (!isDevDuplicate) {
          console.log('✅ Middleware: Unified session found:', {
            hasSession: !!unifiedSession,
            userEmail: unifiedSession.user?.email,
            userId: unifiedSession.user?.id,
            source: unifiedSession.source,
            cookieCount: request.cookies.size,
            authCookieExists: !!request.cookies.get(`sb-${process.env.NEXT_PUBLIC_SUPABASE_URL?.match(/https:\/\/([^.]+)\.supabase\.co/)?.[1] ?? 'anwefmklplkjxkmzpnva'}-auth-token`),
            expiresAt: unifiedSession.expires_at,
            hasAccessToken: !!unifiedSession.access_token
          });
        }
        
        // SESSION CACHE: Store in cache for future requests
        if (isCachingEnabled() && user) {
          setCachedSession(request, session, user, userRoles);
          if (!isDevDuplicate) {
            console.log('💾 Middleware: Session cached for user:', user.email);
          }
        }
        
        // MONITORING: Update session status
        const authTime = Date.now() - authStartTime;
        middlewareMonitor.updateSessionStatus(requestMetrics.requestId, true, authTime);
      } else {
        // MONITORING: Update session status - no session found
        const authTime = Date.now() - authStartTime;
        middlewareMonitor.updateSessionStatus(requestMetrics.requestId, false, authTime);
        console.log('❌ Middleware: No unified session found');

        // Debug: Let's see what cookies we actually have
        const allCookies: {name: string, hasValue: boolean, valueLength?: number}[] = [];
        request.cookies.getAll().forEach(cookie => {
          allCookies.push({
            name: cookie.name,
            hasValue: !!cookie.value,
            valueLength: cookie.value?.length
          });
        });
        console.log('🔍 Middleware: Available cookies:', allCookies);

        // Specifically check the auth cookie (dynamic based on environment)
        const projectRef = process.env.NEXT_PUBLIC_SUPABASE_URL?.match(/https:\/\/([^.]+)\.supabase\.co/)?.[1] ?? 'anwefmklplkjxkmzpnva';
        const authCookie = request.cookies.get(`sb-${projectRef}-auth-token`);
        if (authCookie) {
          console.log('🔍 Middleware: Auth cookie exists but session failed:', {
            cookieLength: authCookie.value.length,
            cookiePreview: authCookie.value.substring(0, 100) + '...'
          });
        }
      }
    } catch (error) {
      console.error('❌ Error getting unified session in middleware:', error);
      console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      
      // MONITORING: Record session error
      const authTime = Date.now() - authStartTime;
      middlewareMonitor.updateSessionStatus(requestMetrics.requestId, false, authTime);
    }
  }

  if (user && !isDevDuplicate) {
    console.log(
      "Middleware: User object from unified session:",
      JSON.stringify(user, null, 2),
    );
    console.log(
      "Middleware: Extracted user_metadata:",
      JSON.stringify(user?.user_metadata, null, 2),
    );
  }
  const currentPath = request.nextUrl.pathname;

  // --- REDIRECT AUTHENTICATED USERS AWAY FROM LOGIN PAGES ---
  if (user && (currentPath === "/login" || currentPath === "/loginadmin")) {
    console.log('🔄 Middleware: Authenticated user accessing login page, redirecting...');

    // Check if user is superadmin
    const userEmail = user.email;
    let isSuperAdmin = false;

    if (isSuperAdminEmail(userEmail)) {
      isSuperAdmin = true;
    } else if (user.user_metadata?.is_super_admin === true) {
      isSuperAdmin = true;
    }

    // Redirect based on user type
    const redirectPath = isSuperAdmin ? "/superadmin" :
                        (user.app_metadata?.role === "client" || user.user_metadata?.role === "client") ? "/client-portal" :
                        "/dashboard";

    console.log(`🔄 Middleware: Redirecting authenticated user from ${currentPath} to ${redirectPath}`);
    return NextResponse.redirect(new URL(redirectPath, request.url));
  }

  // --- ENHANCED Authentication Check ---
  const isProtectedRoute = Object.keys(protectedRoutes).some((route) =>
    currentPath.startsWith(route),
  );
  const isSuperAdminRoute = currentPath.startsWith("/superadmin");

  if (!user && (isProtectedRoute || isSuperAdminRoute)) {
    // SECURITY ENHANCEMENT: Log authentication failure
    await logSecurityEvent(
      SecurityEventType.AUTHENTICATION_FAILURE,
      request,
      {
        attemptedPath: currentPath,
        isProtectedRoute,
        isSuperAdminRoute,
        sessionExists: !!session
      }
    );

    const loginUrl = isSuperAdminRoute ? "/loginadmin" : "/login";
    const redirectUrl = new URL(loginUrl, request.url);
    redirectUrl.searchParams.set("redirectedFrom", currentPath);
    console.log(
      `Middleware: Unauthenticated access to ${currentPath}. Redirecting to ${loginUrl}.`,
    );
    return NextResponse.redirect(redirectUrl);
  }

  // --- Authorization Check (only if user is authenticated) ---
  if (user) {
    const userEmail = user.email;

    // CHECK SUPERADMIN STATUS FIRST (before any role checks)
    let isSuperAdmin = false;

    // Primary check: centralized superadmin email list
    if (isSuperAdminEmail(userEmail)) {
      isSuperAdmin = true;
    }

    // SECURITY: Removed client-side JWT parsing for security
    // Secondary check now skipped - relying on metadata only

    // Tertiary check: user_metadata (fallback)
    if (!isSuperAdmin && user.user_metadata?.is_super_admin === true) {
      isSuperAdmin = true;
    }

    // 1. ENHANCED Superadmin Route Check (by JWT is_super_admin claim)
    if (isSuperAdminRoute) {
      if (!isSuperAdmin) {
        // SECURITY ENHANCEMENT: Log unauthorized superadmin access attempt
        await logSecurityEvent(
          SecurityEventType.UNAUTHORIZED_ACCESS,
          request,
          {
            attemptedPath: currentPath,
            userEmail,
            userId: user.id,
            isSuperAdmin: false,
            sessionSource: session?.source
          }
        );

        console.warn(
          `Middleware: Unauthorized attempt on /superadmin by ${userEmail}. User is not a super admin. Redirecting.`,
        );
        return NextResponse.redirect(
          new URL("/dashboard?error=superadmin_unauthorized", request.url),
        );
      }

      console.log(`Middleware: Super admin access granted to ${userEmail} (source: ${session?.source ?? 'unknown'})`);
      // Allow superadmin access with enhanced security response
      return addSecurityHeaders(response, request);
    }

    // 2. SUPERADMIN BYPASS: If user is superadmin, allow access to all routes
    if (isSuperAdmin) {
      console.log(`Middleware: Superadmin ${userEmail} granted access to ${currentPath} via superadmin bypass`);
      return response;
    }

    // 3. Regular Protected Routes Check (by role) - only for non-superadmin users
    // Get user roles from unified session (or use cached roles)
    if (userRoles.length === 0 && session) {
      userRoles = getUserRoles(session);
    }

    // ENHANCED DEBUG LOGGING (only if not a dev duplicate)
    if (!isDevDuplicate) {
      console.log("🔍 MIDDLEWARE DEBUG:", {
        currentPath,
        userEmail,
        userId: user?.id,
        userRoles,
        userRolesLength: userRoles.length,
        sessionSource: session?.source,
        appMetadata: user?.app_metadata,
        userMetadata: user?.user_metadata,
        hasSession: !!session,
        hasUser: !!user
      });
    }

    // ENHANCED session validation with security logging
    if (!user?.id) {
      // SECURITY ENHANCEMENT: Log invalid session
      await logSecurityEvent(
        SecurityEventType.INVALID_SESSION,
        request,
        {
          userEmail,
          hasUser: !!user,
          hasSession: !!session,
          sessionSource: session?.source
        }
      );

      console.log("Middleware: User ID missing, redirecting to login");
      const loginUrl = "/login";
      const redirectUrl = new URL(loginUrl, request.url);
      redirectUrl.searchParams.set("redirectedFrom", currentPath);
      return NextResponse.redirect(redirectUrl);
    }

    // ENHANCED role validation with security logging
    if (userRoles.length === 0) {
      // SECURITY ENHANCEMENT: Log missing roles
      await logSecurityEvent(
        SecurityEventType.AUTHORIZATION_FAILURE,
        request,
        {
          userEmail,
          userId: user.id,
          reason: 'No roles assigned',
          sessionSource: session?.source
        }
      );

      console.log("Middleware: User roles missing, redirecting to login");
      const loginUrl = "/login";
      const redirectUrl = new URL(loginUrl, request.url);
      redirectUrl.searchParams.set("redirectedFrom", currentPath);
      return NextResponse.redirect(redirectUrl);
    }

    // Convert roles to UserRole enums (userRoles may have been modified above)
    const userRoleEnums: UserRole[] = userRoles.map(role => role as UserRole);

    if (!isDevDuplicate) {
      console.log(
        `Middleware: Authenticated user ${user.email} with roles:`,
        userRoleEnums,
      );
    }

    for (const routePrefix in protectedRoutes) {
      if (currentPath.startsWith(routePrefix)) {
        const requiredRoles = protectedRoutes[routePrefix];

        // ENHANCED DEBUG LOG: What roles did we actually validated? (only if not a dev duplicate)
        if (!isDevDuplicate) {
          console.log(
            `🔍 MIDDLEWARE ROLE CHECK: Checking path ${currentPath}. User roles validated: [${userRoleEnums.join(
              ", ",
            )}]. Required roles: [${requiredRoles.join(", ")}]`,
          );
        }

        // Check if the user has at least one of the required roles
        const hasRequiredRole = userRoleEnums.some((userRole) =>
          requiredRoles.includes(userRole),
        );

        if (!isDevDuplicate) {
          console.log(`🔍 ROLE MATCH RESULT: hasRequiredRole = ${hasRequiredRole}`);
        }

        if (!hasRequiredRole) {
          // SECURITY ENHANCEMENT: Log authorization failure
          await logSecurityEvent(
            SecurityEventType.AUTHORIZATION_FAILURE,
            request,
            {
              userEmail,
              userId: user.id,
              userRoles: userRoleEnums,
              requiredRoles,
              attemptedPath: currentPath,
              reason: 'Insufficient role permissions'
            }
          );

          console.warn(
            `Middleware: Role mismatch for ${currentPath}. User ${userEmail} (roles: ${userRoleEnums.join(",") || "none"}) lacks required roles: ${requiredRoles.join(", ")}. Redirecting.`,
          );

          // PREVENT REDIRECT LOOP: Don't redirect to dashboard if we're already there with an error
          if (currentPath.startsWith("/dashboard") && request.nextUrl.searchParams.has("error")) {
            console.error("Middleware: Redirect loop detected on dashboard. Redirecting to login.");
            return NextResponse.redirect(new URL("/login?error=role_loop", request.url));
          }

          // Redirect unauthorized roles away
          const defaultRedirect = userRoleEnums.includes(UserRole.Client)
            ? "/client-portal"
            : "/login"; // Changed: redirect to login instead of dashboard to prevent loops

          return NextResponse.redirect(
            new URL(defaultRedirect + "?error=role_unauthorized", request.url),
          );
        }
        // Role is valid for this route, break the loop
        if (!isDevDuplicate) {
          console.log(
            `Middleware: Roles '${userRoleEnums.join(",")}' authorized for ${currentPath}.`,
          );
        }
        break;
      }
    }
  }

    // Allow request to proceed for public routes or authorized users
    // SECURITY ENHANCEMENT: Always apply security headers to all responses
    
    // MONITORING: End request tracking successfully
    middlewareMonitor.endRequest(requestMetrics.requestId, 200);
    return secureResponse;
    
  } catch (error) {
    console.error('❌ Middleware execution error:', error);
    
    // MONITORING: End request tracking with error
    middlewareMonitor.endRequest(
      requestMetrics.requestId, 
      500, 
      error instanceof Error ? error : new Error('Unknown middleware error')
    );
    
    // Return error response
    return NextResponse.json(
      { error: 'Internal middleware error' },
      { status: 500 }
    );
  }
}

// Config to specify which paths the middleware should run on
// OPTIMIZED: More precise matching to reduce unnecessary middleware executions
export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|public/|.*\\.(?:svg|png|jpg|jpeg|gif|webp|ico)$).*)'
  ],
};
