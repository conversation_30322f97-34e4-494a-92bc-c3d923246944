{"data_mtime": 1755541222, "dep_lines": [20, 17, 18, 19, 21, 22, 386, 10, 11, 14, 23, 6, 8, 9, 12, 13, 15, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 10, 10, 5, 5, 5, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["unstructured.partition.common.metadata", "unstructured.documents.elements", "unstructured.file_utils.model", "unstructured.partition.common", "unstructured.partition.html", "unstructured.partition.text", "unstructured.partition.auto", "email.policy", "email.utils", "email.message", "unstructured.utils", "__future__", "datetime", "email", "io", "os", "typing", "builtins", "_frozen_importlib", "_io", "abc", "typing_extensions", "unstructured.documents"], "hash": "5b1e46193673131710c0094dee1da63b65e05d67", "id": "unstructured.partition.email", "ignore_all": true, "interface_hash": "35083ff99d6dd5b85777c1cb28585988b6de5466", "mtime": 1753678816, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/unstructured/partition/email.py", "plugin_data": null, "size": 16881, "suppressed": [], "version_id": "1.17.1"}