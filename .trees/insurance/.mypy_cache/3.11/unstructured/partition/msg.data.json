{".class": "MypyFile", "_fullname": "unstructured.partition.msg", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Attachment": {".class": "SymbolTableNode", "cross_ref": "oxmsg.attachment.Attachment", "kind": "Gdef"}, "Element": {".class": "SymbolTableNode", "cross_ref": "unstructured.documents.elements.Element", "kind": "Gdef"}, "ElementMetadata": {".class": "SymbolTableNode", "cross_ref": "unstructured.documents.elements.ElementMetadata", "kind": "Gdef"}, "FileType": {".class": "SymbolTableNode", "cross_ref": "unstructured.file_utils.model.FileType", "kind": "Gdef"}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Message": {".class": "SymbolTableNode", "cross_ref": "oxmsg.message.Message", "kind": "Gdef"}, "MsgPartitionerOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "unstructured.partition.msg.MsgPartitionerOptions", "name": "MsgPartitionerOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "unstructured.partition.msg.MsgPartitionerOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "unstructured.partition.msg", "mro": ["unstructured.partition.msg.MsgPartitionerOptions", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "file", "file_path", "metadata_file_path", "metadata_last_modified", "partition_attachments", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "file", "file_path", "metadata_file_path", "metadata_last_modified", "partition_attachments", "kwargs"], "arg_types": ["unstructured.partition.msg.MsgPartitionerOptions", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MsgPartitionerOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions._file", "name": "_file", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_file_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions._file_path", "name": "_file_path", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions._kwargs", "name": "_kwargs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_last_modified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions._last_modified", "name": "_last_modified", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_last_modified of MsgPartitionerOptions", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions._last_modified", "name": "_last_modified", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "_metadata_file_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions._metadata_file_path", "name": "_metadata_file_path", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_metadata_last_modified": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions._metadata_last_modified", "name": "_metadata_last_modified", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_msg_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions._msg_file", "name": "_msg_file", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_msg_file of MsgPartitionerOptions", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions._msg_file", "name": "_msg_file", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "_partition_attachments": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions._partition_attachments", "name": "_partition_attachments", "setter_type": null, "type": "builtins.bool"}}, "extra_msg_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.extra_msg_metadata", "name": "extra_msg_metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extra_msg_metadata of MsgPartitionerOptions", "ret_type": "unstructured.documents.elements.ElementMetadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.extra_msg_metadata", "name": "extra_msg_metadata", "setter_type": null, "type": {".class": "Instance", "args": ["unstructured.documents.elements.ElementMetadata"], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "is_encrypted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.is_encrypted", "name": "is_encrypted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_encrypted of MsgPartitionerOptions", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.is_encrypted", "name": "is_encrypted", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "metadata_file_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.metadata_file_path", "name": "metadata_file_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "metadata_file_path of MsgPartitionerOptions", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.metadata_file_path", "name": "metadata_file_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "metadata_last_modified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.metadata_last_modified", "name": "metadata_last_modified", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "metadata_last_modified of MsgPartitionerOptions", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.metadata_last_modified", "name": "metadata_last_modified", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "msg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.msg", "name": "msg", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "msg of MsgPartitionerOptions", "ret_type": "oxmsg.message.Message", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.msg", "name": "msg", "setter_type": null, "type": {".class": "Instance", "args": ["oxmsg.message.Message"], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "partition_attachments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.partition_attachments", "name": "partition_attachments", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "partition_attachments of MsgPartitionerOptions", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.partition_attachments", "name": "partition_attachments", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "partitioning_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.partitioning_kwargs", "name": "partitioning_kwargs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "partitioning_kwargs of MsgPartitionerOptions", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg.MsgPartitionerOptions.partitioning_kwargs", "name": "partitioning_kwargs", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "unstructured.partition.msg.MsgPartitionerOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "unstructured.partition.msg.MsgPartitionerOptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "UnsupportedFileFormatError": {".class": "SymbolTableNode", "cross_ref": "unstructured.partition.common.UnsupportedFileFormatError", "kind": "Gdef"}, "_AttachmentPartitioner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "unstructured.partition.msg._AttachmentPartitioner", "name": "_AttachmentPartitioner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "unstructured.partition.msg._AttachmentPartitioner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "unstructured.partition.msg", "mro": ["unstructured.partition.msg._AttachmentPartitioner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "attachment", "opts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unstructured.partition.msg._AttachmentPartitioner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "attachment", "opts"], "arg_types": ["unstructured.partition.msg._AttachmentPartitioner", "oxmsg.attachment.Attachment", "unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _AttachmentPartitioner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_attachment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.msg._AttachmentPartitioner._attachment", "name": "_attachment", "setter_type": null, "type": "oxmsg.attachment.Attachment"}}, "_attachment_file_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg._AttachmentPartitioner._attachment_file_name", "name": "_attachment_file_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg._AttachmentPartitioner"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_attachment_file_name of _AttachmentPartitioner", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg._AttachmentPartitioner._attachment_file_name", "name": "_attachment_file_name", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "_attachment_last_modified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg._AttachmentPartitioner._attachment_last_modified", "name": "_attachment_last_modified", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg._AttachmentPartitioner"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_attachment_last_modified of _AttachmentPartitioner", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg._AttachmentPartitioner._attachment_last_modified", "name": "_attachment_last_modified", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "_file_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg._AttachmentPartitioner._file_bytes", "name": "_file_bytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg._AttachmentPartitioner"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_file_bytes of _AttachmentPartitioner", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg._AttachmentPartitioner._file_bytes", "name": "_file_bytes", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "_iter_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_trivial_self"], "fullname": "unstructured.partition.msg._AttachmentPartitioner._iter_elements", "name": "_iter_elements", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg._AttachmentPartitioner"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_iter_elements of _AttachmentPartitioner", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_opts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.msg._AttachmentPartitioner._opts", "name": "_opts", "setter_type": null, "type": "unstructured.partition.msg.MsgPartitionerOptions"}}, "iter_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "attachment", "opts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg._AttachmentPartitioner.iter_elements", "name": "iter_elements", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "attachment", "opts"], "arg_types": [{".class": "TypeType", "item": "unstructured.partition.msg._AttachmentPartitioner"}, "oxmsg.attachment.Attachment", "unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iter_elements of _AttachmentPartitioner", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg._AttachmentPartitioner.iter_elements", "name": "iter_elements", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "attachment", "opts"], "arg_types": [{".class": "TypeType", "item": "unstructured.partition.msg._AttachmentPartitioner"}, "oxmsg.attachment.Attachment", "unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iter_elements of _AttachmentPartitioner", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "unstructured.partition.msg._AttachmentPartitioner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "unstructured.partition.msg._AttachmentPartitioner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MsgPartitioner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "unstructured.partition.msg._MsgPartitioner", "name": "_MsgPartitioner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "unstructured.partition.msg._MsgPartitioner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "unstructured.partition.msg", "mro": ["unstructured.partition.msg._MsgPartitioner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "opts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unstructured.partition.msg._MsgPartitioner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "opts"], "arg_types": ["unstructured.partition.msg._MsgPartitioner", "unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _MsgPartitioner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_attachments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg._MsgPartitioner._attachments", "name": "_attachments", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg._MsgPartitioner"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_attachments of _MsgPartitioner", "ret_type": {".class": "Instance", "args": ["oxmsg.attachment.Attachment"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg._MsgPartitioner._attachments", "name": "_attachments", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["oxmsg.attachment.Attachment"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "_iter_message_body_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unstructured.partition.msg._MsgPartitioner._iter_message_body_elements", "name": "_iter_message_body_elements", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg._MsgPartitioner"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_iter_message_body_elements of _MsgPartitioner", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iter_message_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unstructured.partition.msg._MsgPartitioner._iter_message_elements", "name": "_iter_message_elements", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.msg._MsgPartitioner"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_iter_message_elements of _MsgPartitioner", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_opts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.msg._MsgPartitioner._opts", "name": "_opts", "setter_type": null, "type": "unstructured.partition.msg.MsgPartitionerOptions"}}, "iter_message_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "opts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.msg._MsgPartitioner.iter_message_elements", "name": "iter_message_elements", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "opts"], "arg_types": [{".class": "TypeType", "item": "unstructured.partition.msg._MsgPartitioner"}, "unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iter_message_elements of _MsgPartitioner", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "unstructured.partition.msg._MsgPartitioner.iter_message_elements", "name": "iter_message_elements", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "opts"], "arg_types": [{".class": "TypeType", "item": "unstructured.partition.msg._MsgPartitioner"}, "unstructured.partition.msg.MsgPartitionerOptions"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iter_message_elements of _MsgPartitioner", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "unstructured.partition.msg._MsgPartitioner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "unstructured.partition.msg._MsgPartitioner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.msg.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.msg.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.msg.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.msg.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.msg.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.msg.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "get_last_modified_date": {".class": "SymbolTableNode", "cross_ref": "unstructured.partition.common.metadata.get_last_modified_date", "kind": "Gdef"}, "is_temp_file_path": {".class": "SymbolTableNode", "cross_ref": "unstructured.utils.is_temp_file_path", "kind": "Gdef"}, "lazyproperty": {".class": "SymbolTableNode", "cross_ref": "unstructured.utils.lazyproperty", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "cross_ref": "unstructured.logger.logger", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "partition_html": {".class": "SymbolTableNode", "cross_ref": "unstructured.partition.html.partition.partition_html", "kind": "Gdef"}, "partition_msg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 5, 4], "arg_names": ["filename", "file", "metadata_filename", "metadata_last_modified", "process_attachments", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unstructured.partition.msg.partition_msg", "name": "partition_msg", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 4], "arg_names": ["filename", "file", "metadata_filename", "metadata_last_modified", "process_attachments", "kwargs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "partition_msg", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "partition_text": {".class": "SymbolTableNode", "cross_ref": "unstructured.partition.text.partition_text", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/unstructured/partition/msg.py"}