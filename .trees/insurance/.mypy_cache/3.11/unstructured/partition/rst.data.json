{".class": "MypyFile", "_fullname": "unstructured.partition.rst", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "DETECTION_ORIGIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "unstructured.partition.rst.DETECTION_ORIGIN", "name": "DETECTION_ORIGIN", "setter_type": null, "type": "builtins.str"}}, "Element": {".class": "SymbolTableNode", "cross_ref": "unstructured.documents.elements.Element", "kind": "Gdef"}, "FileType": {".class": "SymbolTableNode", "cross_ref": "unstructured.file_utils.model.FileType", "kind": "Gdef"}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.rst.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.rst.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.rst.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.rst.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.rst.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.rst.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "convert_file_to_html_text_using_pandoc": {".class": "SymbolTableNode", "cross_ref": "unstructured.file_utils.file_conversion.convert_file_to_html_text_using_pandoc", "kind": "Gdef"}, "exactly_one": {".class": "SymbolTableNode", "cross_ref": "unstructured.partition.common.common.exactly_one", "kind": "Gdef"}, "get_last_modified_date": {".class": "SymbolTableNode", "cross_ref": "unstructured.partition.common.metadata.get_last_modified_date", "kind": "Gdef"}, "partition_html": {".class": "SymbolTableNode", "cross_ref": "unstructured.partition.html.partition.partition_html", "kind": "Gdef"}, "partition_rst": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 4], "arg_names": ["filename", "file", "metadata_filename", "metadata_last_modified", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "unstructured.partition.rst.partition_rst", "name": "partition_rst", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 4], "arg_names": ["filename", "file", "metadata_filename", "metadata_last_modified", "kwargs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "partition_rst", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/opt/homebrew/lib/python3.12/site-packages/unstructured/partition/rst.py"}