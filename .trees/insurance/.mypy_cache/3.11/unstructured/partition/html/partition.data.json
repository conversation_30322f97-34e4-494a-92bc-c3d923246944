{".class": "MypyFile", "_fullname": "unstructured.partition.html.partition", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Element": {".class": "SymbolTableNode", "cross_ref": "unstructured.documents.elements.Element", "kind": "Gdef"}, "ElementType": {".class": "SymbolTableNode", "cross_ref": "unstructured.documents.elements.ElementType", "kind": "Gdef"}, "FileType": {".class": "SymbolTableNode", "cross_ref": "unstructured.file_utils.model.FileType", "kind": "Gdef"}, "Flow": {".class": "SymbolTableNode", "cross_ref": "unstructured.partition.html.parser.Flow", "kind": "Gdef"}, "HtmlPartitionerOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions", "name": "HtmlPartitionerOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "unstructured.partition.html.partition", "mro": ["unstructured.partition.html.partition.HtmlPartitionerOptions", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 5, 5, 5, 5], "arg_names": ["self", "file_path", "file", "text", "encoding", "url", "headers", "ssl_verify", "skip_headers_and_footers", "detection_origin", "html_parser_version", "image_alt_mode", "extract_image_block_types", "extract_image_block_to_payload"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 5, 5, 5, 5], "arg_names": ["self", "file_path", "file", "text", "encoding", "url", "headers", "ssl_verify", "skip_headers_and_footers", "detection_origin", "html_parser_version", "image_alt_mode", "extract_image_block_types", "extract_image_block_to_payload"], "arg_types": ["unstructured.partition.html.partition.HtmlPartitionerOptions", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "v1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "v2"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "to_text"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of HtmlPartitionerOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_detection_origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions._detection_origin", "name": "_detection_origin", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_encoding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions._encoding", "name": "_encoding", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_extract_image_block_to_payload": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions._extract_image_block_to_payload", "name": "_extract_image_block_to_payload", "setter_type": null, "type": "builtins.bool"}}, "_extract_image_block_types": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions._extract_image_block_types", "name": "_extract_image_block_types", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions._file", "name": "_file", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_file_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions._file_path", "name": "_file_path", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions._headers", "name": "_headers", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_html_parser_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions._html_parser_version", "name": "_html_parser_version", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "v1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "v2"}], "uses_pep604_syntax": false}}}, "_image_alt_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions._image_alt_mode", "name": "_image_alt_mode", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "to_text"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_skip_headers_and_footers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions._skip_headers_and_footers", "name": "_skip_headers_and_footers", "setter_type": null, "type": "builtins.bool"}}, "_ssl_verify": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions._ssl_verify", "name": "_ssl_verify", "setter_type": null, "type": "builtins.bool"}}, "_text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions._text", "name": "_text", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions._url", "name": "_url", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "add_img_alt_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions.add_img_alt_text", "name": "add_img_alt_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.html.partition.HtmlPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_img_alt_text of HtmlPartitionerOptions", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions.add_img_alt_text", "name": "add_img_alt_text", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "detection_origin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions.detection_origin", "name": "detection_origin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.html.partition.HtmlPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "detection_origin of HtmlPartitionerOptions", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions.detection_origin", "name": "detection_origin", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "html_parser_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions.html_parser_version", "name": "html_parser_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.html.partition.HtmlPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "html_parser_version of HtmlPartitionerOptions", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "v1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "v2"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions.html_parser_version", "name": "html_parser_version", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "v1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "v2"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "html_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions.html_text", "name": "html_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.html.partition.HtmlPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "html_text of HtmlPartitionerOptions", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions.html_text", "name": "html_text", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "last_modified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions.last_modified", "name": "last_modified", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.html.partition.HtmlPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "last_modified of HtmlPartitionerOptions", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions.last_modified", "name": "last_modified", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "skip_headers_and_footers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions.skip_headers_and_footers", "name": "skip_headers_and_footers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.html.partition.HtmlPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "skip_headers_and_footers of HtmlPartitionerOptions", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions.skip_headers_and_footers", "name": "skip_headers_and_footers", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "unstructured.partition.html.partition.HtmlPartitionerOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "unstructured.partition.html.partition.HtmlPartitionerOptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "_HtmlPartitioner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "unstructured.partition.html.partition._HtmlPartitioner", "name": "_HtmlPartitioner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "unstructured.partition.html.partition._HtmlPartitioner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "unstructured.partition.html.partition", "mro": ["unstructured.partition.html.partition._HtmlPartitioner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "opts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unstructured.partition.html.partition._HtmlPartitioner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "opts"], "arg_types": ["unstructured.partition.html.partition._HtmlPartitioner", "unstructured.partition.html.partition.HtmlPartitionerOptions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _HtmlPartitioner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_from_ontology": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.html.partition._HtmlPartitioner._from_ontology", "name": "_from_ontology", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.html.partition._HtmlPartitioner"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_from_ontology of _HtmlPartitioner", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.html.partition._HtmlPartitioner._from_ontology", "name": "_from_ontology", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "_iter_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_trivial_self"], "fullname": "unstructured.partition.html.partition._HtmlPartitioner._iter_elements", "name": "_iter_elements", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.html.partition._HtmlPartitioner"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_iter_elements of _HtmlPartitioner", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_main": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.html.partition._HtmlPartitioner._main", "name": "_main", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["unstructured.partition.html.partition._HtmlPartitioner"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_main of _HtmlPartitioner", "ret_type": "unstructured.partition.html.parser.Flow", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "unstructured.partition.html.partition._HtmlPartitioner._main", "name": "_main", "setter_type": null, "type": {".class": "Instance", "args": ["unstructured.partition.html.parser.Flow"], "extra_attrs": null, "type_ref": "unstructured.utils.lazyproperty"}}}}, "_opts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "unstructured.partition.html.partition._HtmlPartitioner._opts", "name": "_opts", "setter_type": null, "type": "unstructured.partition.html.partition.HtmlPartitionerOptions"}}, "_should_include_image_base64": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "unstructured.partition.html.partition._HtmlPartitioner._should_include_image_base64", "name": "_should_include_image_base64", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "element"], "arg_types": ["unstructured.partition.html.partition._HtmlPartitioner", "unstructured.documents.elements.Element"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_should_include_image_base64 of _HtmlPartitioner", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "opts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "unstructured.partition.html.partition._HtmlPartitioner.iter_elements", "name": "iter_elements", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "opts"], "arg_types": [{".class": "TypeType", "item": "unstructured.partition.html.partition._HtmlPartitioner"}, "unstructured.partition.html.partition.HtmlPartitionerOptions"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iter_elements of _HtmlPartitioner", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "unstructured.partition.html.partition._HtmlPartitioner.iter_elements", "name": "iter_elements", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "opts"], "arg_types": [{".class": "TypeType", "item": "unstructured.partition.html.partition._HtmlPartitioner"}, "unstructured.partition.html.partition.HtmlPartitionerOptions"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iter_elements of _HtmlPartitioner", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "unstructured.partition.html.partition._HtmlPartitioner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "unstructured.partition.html.partition._HtmlPartitioner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.html.partition.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.html.partition.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.html.partition.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.html.partition.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.html.partition.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "unstructured.partition.html.partition.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "add_chunking_strategy": {".class": "SymbolTableNode", "cross_ref": "unstructured.chunking.dispatch.add_chunking_strategy", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "apply_metadata": {".class": "SymbolTableNode", "cross_ref": "unstructured.partition.common.metadata.apply_metadata", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "etree": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "unstructured.partition.html.partition.etree", "name": "etree", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "unstructured.partition.html.partition.etree", "source_any": null, "type_of_any": 3}}}, "get_last_modified_date": {".class": "SymbolTableNode", "cross_ref": "unstructured.partition.common.metadata.get_last_modified_date", "kind": "Gdef"}, "html_parser": {".class": "SymbolTableNode", "cross_ref": "unstructured.partition.html.parser.html_parser", "kind": "Gdef"}, "is_temp_file_path": {".class": "SymbolTableNode", "cross_ref": "unstructured.utils.is_temp_file_path", "kind": "Gdef"}, "lazyproperty": {".class": "SymbolTableNode", "cross_ref": "unstructured.utils.lazyproperty", "kind": "Gdef"}, "ontology_to_unstructured_elements": {".class": "SymbolTableNode", "cross_ref": "unstructured.partition.html.transformations.ontology_to_unstructured_elements", "kind": "Gdef"}, "parse_html_to_ontology": {".class": "SymbolTableNode", "cross_ref": "unstructured.partition.html.transformations.parse_html_to_ontology", "kind": "Gdef"}, "partition_html": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["filename", "file", "text", "encoding", "url", "headers", "ssl_verify", "skip_headers_and_footers", "detection_origin", "html_parser_version", "image_alt_mode", "extract_image_block_to_payload", "extract_image_block_types", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "unstructured.partition.html.partition.partition_html", "name": "partition_html", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["filename", "file", "text", "encoding", "url", "headers", "ssl_verify", "skip_headers_and_footers", "detection_origin", "html_parser_version", "image_alt_mode", "extract_image_block_to_payload", "extract_image_block_types", "kwargs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "v1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "v2"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "to_text"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "partition_html", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "unstructured.partition.html.partition.partition_html", "name": "partition_html", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["filename", "file", "text", "encoding", "url", "headers", "ssl_verify", "skip_headers_and_footers", "detection_origin", "html_parser_version", "image_alt_mode", "extract_image_block_to_payload", "extract_image_block_types", "kwargs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "v1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "v2"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "to_text"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "partition_html", "ret_type": {".class": "Instance", "args": ["unstructured.documents.elements.Element"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "read_txt_file": {".class": "SymbolTableNode", "cross_ref": "unstructured.file_utils.encoding.read_txt_file", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "unstructured.partition.html.partition.requests", "name": "requests", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "unstructured.partition.html.partition.requests", "source_any": null, "type_of_any": 3}}}}, "path": "/opt/homebrew/lib/python3.12/site-packages/unstructured/partition/html/partition.py"}