{"data_mtime": 1755541222, "dep_lines": [6, 19, 29, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pinecone.db_control.models", "pinecone.db_control.enums", "pinecone.db_control.types", "abc", "typing", "builtins", "_frozen_importlib", "enum", "pinecone.db_control", "pinecone.db_control.enums.clouds", "pinecone.db_control.enums.deletion_protection", "pinecone.db_control.enums.metric", "pinecone.db_control.enums.pod_type", "pinecone.db_control.enums.vector_type", "pinecone.db_control.models.backup_list", "pinecone.db_control.models.backup_model", "pinecone.db_control.models.byoc_spec", "pinecone.db_control.models.collection_list", "pinecone.db_control.models.index_list", "pinecone.db_control.models.index_model", "pinecone.db_control.models.pod_spec", "pinecone.db_control.models.restore_job_list", "pinecone.db_control.models.restore_job_model", "pinecone.db_control.models.serverless_spec", "pinecone.db_control.types.configure_index_embed", "pinecone.db_control.types.create_index_for_model_embed", "pinecone.inference", "pinecone.inference.inference_request_builder", "pinecone.inference.models", "pinecone.inference.models.index_embed"], "hash": "7d64a475a0ad3a333b5e2a528a244af3d8cea6fc", "id": "pinecone.legacy_pinecone_interface", "ignore_all": true, "interface_hash": "855e20bbdd28c6ca5d57d6cb4abbfb369b7c1996", "mtime": 1753678816, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/pinecone/legacy_pinecone_interface.py", "plugin_data": null, "size": 31786, "suppressed": [], "version_id": "1.17.1"}