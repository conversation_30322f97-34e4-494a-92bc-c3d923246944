{".class": "MypyFile", "_fullname": "pinecone.legacy_pinecone_interface", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "AwsRegion": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.AwsRegion", "kind": "Gdef"}, "AzureRegion": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.AzureRegion", "kind": "Gdef"}, "BackupList": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.backup_list.BackupList", "kind": "Gdef"}, "BackupModel": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.backup_model.BackupModel", "kind": "Gdef"}, "ByocSpec": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.byoc_spec.ByocSpec", "kind": "Gdef"}, "CloudProvider": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.CloudProvider", "kind": "Gdef"}, "CollectionList": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.collection_list.CollectionList", "kind": "Gdef"}, "ConfigureIndexEmbed": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.types.configure_index_embed.ConfigureIndexEmbed", "kind": "Gdef"}, "CreateIndexForModelEmbedTypedDict": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.types.create_index_for_model_embed.CreateIndexForModelEmbedTypedDict", "kind": "Gdef"}, "DeletionProtection": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.deletion_protection.DeletionProtection", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GcpRegion": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.GcpRegion", "kind": "Gdef"}, "IndexEmbed": {".class": "SymbolTableNode", "cross_ref": "pinecone.inference.models.index_embed.IndexEmbed", "kind": "Gdef"}, "IndexList": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.index_list.IndexList", "kind": "Gdef"}, "IndexModel": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.index_model.IndexModel", "kind": "Gdef"}, "LegacyPineconeDBControlInterface": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["Index", 1], ["__init__", 1], ["configure_index", 1], ["create_backup", 1], ["create_collection", 1], ["create_index", 1], ["create_index_for_model", 1], ["create_index_from_backup", 1], ["delete_backup", 1], ["delete_collection", 1], ["delete_index", 1], ["describe_backup", 1], ["describe_collection", 1], ["describe_index", 1], ["describe_restore_job", 1], ["has_index", 1], ["list_backups", 1], ["list_collections", 1], ["list_indexes", 1], ["list_restore_jobs", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "name": "LegacyPineconeDBControlInterface", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pinecone.legacy_pinecone_interface", "mro": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "Index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "name", "host", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.Index", "name": "Index", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "name", "host", "kwargs"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "Index of LegacyPineconeDBControlInterface", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.Index", "name": "Index", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "name", "host", "kwargs"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "Index of LegacyPineconeDBControlInterface", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "IndexAsyncio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "host", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.IndexAsyncio", "name": "IndexAsyncio", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "host", "kwargs"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "IndexAsyncio of LegacyPineconeDBControlInterface", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "api_key", "host", "proxy_url", "proxy_headers", "ssl_ca_certs", "ssl_verify", "additional_headers", "pool_threads", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "api_key", "host", "proxy_url", "proxy_headers", "ssl_ca_certs", "ssl_verify", "additional_headers", "pool_threads", "kwargs"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of LegacyPineconeDBControlInterface", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.__init__", "name": "__init__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "api_key", "host", "proxy_url", "proxy_headers", "ssl_ca_certs", "ssl_verify", "additional_headers", "pool_threads", "kwargs"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of LegacyPineconeDBControlInterface", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "configure_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "replicas", "pod_type", "deletion_protection", "tags", "embed"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.configure_index", "name": "configure_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "replicas", "pod_type", "deletion_protection", "tags", "embed"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.pod_type.PodType", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_control.types.configure_index_embed.ConfigureIndexEmbed"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "configure_index of LegacyPineconeDBControlInterface", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.configure_index", "name": "configure_index", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "replicas", "pod_type", "deletion_protection", "tags", "embed"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.pod_type.PodType", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_control.types.configure_index_embed.ConfigureIndexEmbed"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "configure_index of LegacyPineconeDBControlInterface", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_backup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 3, 3, 5], "arg_names": ["self", "index_name", "backup_name", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.create_backup", "name": "create_backup", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5], "arg_names": ["self", "index_name", "backup_name", "description"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_backup of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.backup_model.BackupModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.create_backup", "name": "create_backup", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5], "arg_names": ["self", "index_name", "backup_name", "description"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_backup of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.backup_model.BackupModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.create_collection", "name": "create_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "source"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_collection of LegacyPineconeDBControlInterface", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.create_collection", "name": "create_collection", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "source"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_collection of LegacyPineconeDBControlInterface", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "spec", "dimension", "metric", "timeout", "deletion_protection", "vector_type", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.create_index", "name": "create_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "spec", "dimension", "metric", "timeout", "deletion_protection", "vector_type", "tags"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "pinecone.db_control.models.serverless_spec.ServerlessSpec", "pinecone.db_control.models.pod_spec.PodSpec", "pinecone.db_control.models.byoc_spec.ByocSpec"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.metric.Metric", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.vector_type.VectorType", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_index of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.index_model.IndexModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.create_index", "name": "create_index", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "spec", "dimension", "metric", "timeout", "deletion_protection", "vector_type", "tags"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "pinecone.db_control.models.serverless_spec.ServerlessSpec", "pinecone.db_control.models.pod_spec.PodSpec", "pinecone.db_control.models.byoc_spec.ByocSpec"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.metric.Metric", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.vector_type.VectorType", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_index of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.index_model.IndexModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_index_for_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 3, 3, 3, 3, 5, 5, 5], "arg_names": ["self", "name", "cloud", "region", "embed", "tags", "deletion_protection", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.create_index_for_model", "name": "create_index_for_model", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 5, 5, 5], "arg_names": ["self", "name", "cloud", "region", "embed", "tags", "deletion_protection", "timeout"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", {".class": "UnionType", "items": ["pinecone.db_control.enums.clouds.CloudProvider", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.clouds.AwsRegion", "pinecone.db_control.enums.clouds.GcpRegion", "pinecone.db_control.enums.clouds.AzureRegion", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.inference.models.index_embed.IndexEmbed", {".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_control.types.create_index_for_model_embed.CreateIndexForModelEmbedTypedDict"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_index_for_model of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.index_model.IndexModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.create_index_for_model", "name": "create_index_for_model", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 5, 5, 5], "arg_names": ["self", "name", "cloud", "region", "embed", "tags", "deletion_protection", "timeout"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", {".class": "UnionType", "items": ["pinecone.db_control.enums.clouds.CloudProvider", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.clouds.AwsRegion", "pinecone.db_control.enums.clouds.GcpRegion", "pinecone.db_control.enums.clouds.AzureRegion", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.inference.models.index_embed.IndexEmbed", {".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_control.types.create_index_for_model_embed.CreateIndexForModelEmbedTypedDict"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_index_for_model of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.index_model.IndexModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_index_from_backup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 3, 3, 5, 5, 5], "arg_names": ["self", "name", "backup_id", "deletion_protection", "tags", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.create_index_from_backup", "name": "create_index_from_backup", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5], "arg_names": ["self", "name", "backup_id", "deletion_protection", "tags", "timeout"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_index_from_backup of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.index_model.IndexModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.create_index_from_backup", "name": "create_index_from_backup", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5], "arg_names": ["self", "name", "backup_id", "deletion_protection", "tags", "timeout"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_index_from_backup of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.index_model.IndexModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_backup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 3], "arg_names": ["self", "backup_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.delete_backup", "name": "delete_backup", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "backup_id"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_backup of LegacyPineconeDBControlInterface", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.delete_backup", "name": "delete_backup", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "backup_id"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_backup of LegacyPineconeDBControlInterface", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.delete_collection", "name": "delete_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_collection of LegacyPineconeDBControlInterface", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.delete_collection", "name": "delete_collection", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_collection of LegacyPineconeDBControlInterface", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.delete_index", "name": "delete_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "timeout"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_index of LegacyPineconeDBControlInterface", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.delete_index", "name": "delete_index", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "timeout"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_index of LegacyPineconeDBControlInterface", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "describe_backup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 3], "arg_names": ["self", "backup_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.describe_backup", "name": "describe_backup", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "backup_id"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe_backup of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.backup_model.BackupModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.describe_backup", "name": "describe_backup", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "backup_id"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe_backup of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.backup_model.BackupModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "describe_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.describe_collection", "name": "describe_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe_collection of LegacyPineconeDBControlInterface", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.describe_collection", "name": "describe_collection", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe_collection of LegacyPineconeDBControlInterface", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "describe_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.describe_index", "name": "describe_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe_index of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.index_model.IndexModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.describe_index", "name": "describe_index", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe_index of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.index_model.IndexModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "describe_restore_job": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 3], "arg_names": ["self", "job_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.describe_restore_job", "name": "describe_restore_job", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "job_id"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe_restore_job of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.restore_job_model.RestoreJobModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.describe_restore_job", "name": "describe_restore_job", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "job_id"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe_restore_job of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.restore_job_model.RestoreJobModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.has_index", "name": "has_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_index of LegacyPineconeDBControlInterface", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.has_index", "name": "has_index", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_index of LegacyPineconeDBControlInterface", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_backups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "index_name", "limit", "pagination_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.list_backups", "name": "list_backups", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "index_name", "limit", "pagination_token"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_backups of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.backup_list.BackupList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.list_backups", "name": "list_backups", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "index_name", "limit", "pagination_token"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_backups of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.backup_list.BackupList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_collections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.list_collections", "name": "list_collections", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_collections of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.collection_list.CollectionList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.list_collections", "name": "list_collections", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_collections of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.collection_list.CollectionList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.list_indexes", "name": "list_indexes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_indexes of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.index_list.IndexList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.list_indexes", "name": "list_indexes", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_indexes of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.index_list.IndexList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_restore_jobs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 5, 5], "arg_names": ["self", "limit", "pagination_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.list_restore_jobs", "name": "list_restore_jobs", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "limit", "pagination_token"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_restore_jobs of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.restore_job_list.RestoreJobList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.list_restore_jobs", "name": "list_restore_jobs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "limit", "pagination_token"], "arg_types": ["pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_restore_jobs of LegacyPineconeDBControlInterface", "ret_type": "pinecone.db_control.models.restore_job_list.RestoreJobList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pinecone.legacy_pinecone_interface.LegacyPineconeDBControlInterface", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Metric": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.metric.Metric", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PodSpec": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.pod_spec.PodSpec", "kind": "Gdef"}, "PodType": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.pod_type.PodType", "kind": "Gdef"}, "RestoreJobList": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.restore_job_list.RestoreJobList", "kind": "Gdef"}, "RestoreJobModel": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.restore_job_model.RestoreJobModel", "kind": "Gdef"}, "ServerlessSpec": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.serverless_spec.ServerlessSpec", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "VectorType": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.vector_type.VectorType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.legacy_pinecone_interface.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.legacy_pinecone_interface.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.legacy_pinecone_interface.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.legacy_pinecone_interface.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.legacy_pinecone_interface.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.legacy_pinecone_interface.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/pinecone/legacy_pinecone_interface.py"}