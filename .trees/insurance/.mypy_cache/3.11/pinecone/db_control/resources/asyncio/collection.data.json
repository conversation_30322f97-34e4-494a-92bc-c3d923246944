{".class": "MypyFile", "_fullname": "pinecone.db_control.resources.asyncio.collection", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CollectionList": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.collection_list.CollectionList", "kind": "Gdef"}, "CollectionResourceAsyncio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio", "name": "CollectionResourceAsyncio", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pinecone.db_control.resources.asyncio.collection", "mro": ["pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index_api"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio.__init__", "name": "__init__", "type": null}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["self", "name", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["self", "name", "source"], "arg_types": ["pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of CollectionResourceAsyncio", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio.create", "name": "create", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "name"], "arg_types": ["pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of CollectionResourceAsyncio", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio.delete", "name": "delete", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "describe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio.describe", "name": "describe", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "name"], "arg_types": ["pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe of CollectionResourceAsyncio", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio.describe", "name": "describe", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "index_api": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio.index_api", "name": "index_api", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio.list", "name": "list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list of CollectionResourceAsyncio", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pinecone.db_control.models.collection_list.CollectionList"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio.list", "name": "list", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pinecone.db_control.resources.asyncio.collection.CollectionResourceAsyncio", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PineconeDBControlRequestFactory": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.asyncio.collection.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.asyncio.collection.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.asyncio.collection.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.asyncio.collection.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.asyncio.collection.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.asyncio.collection.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pinecone.db_control.resources.asyncio.collection.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "require_kwargs": {".class": "SymbolTableNode", "cross_ref": "pinecone.utils.require_kwargs.require_kwargs", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/pinecone/db_control/resources/asyncio/collection.py"}