{".class": "MypyFile", "_fullname": "pinecone.db_control.resources.asyncio.index", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "API_VERSION": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.API_VERSION", "kind": "Gdef"}, "AwsRegion": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.AwsRegion", "kind": "Gdef"}, "AzureRegion": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.AzureRegion", "kind": "Gdef"}, "ByocSpec": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.byoc_spec.ByocSpec", "kind": "Gdef"}, "CloudProvider": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.CloudProvider", "kind": "Gdef"}, "ConfigureIndexEmbed": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.types.configure_index_embed.ConfigureIndexEmbed", "kind": "Gdef"}, "CreateIndexForModelEmbedTypedDict": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.types.create_index_for_model_embed.CreateIndexForModelEmbedTypedDict", "kind": "Gdef"}, "DeletionProtection": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.deletion_protection.DeletionProtection", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GcpRegion": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.GcpRegion", "kind": "Gdef"}, "IndexEmbed": {".class": "SymbolTableNode", "cross_ref": "pinecone.inference.models.index_embed.IndexEmbed", "kind": "Gdef"}, "IndexList": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.index_list.IndexList", "kind": "Gdef"}, "IndexModel": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.index_model.IndexModel", "kind": "Gdef"}, "IndexResourceAsyncio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio", "name": "IndexResourceAsyncio", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pinecone.db_control.resources.asyncio.index", "mro": ["pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "index_api", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.__init__", "name": "__init__", "type": null}}, "__poll_describe_index_until_ready": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.__poll_describe_index_until_ready", "name": "__poll_describe_index_until_ready", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "timeout"], "arg_types": ["pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__poll_describe_index_until_ready of IndexResourceAsyncio", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio._config", "name": "_config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_index_api": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio._index_api", "name": "_index_api", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "configure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "replicas", "pod_type", "deletion_protection", "tags", "embed"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.configure", "name": "configure", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "replicas", "pod_type", "deletion_protection", "tags", "embed"], "arg_types": ["pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.pod_type.PodType", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_control.types.configure_index_embed.ConfigureIndexEmbed"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "configure of IndexResourceAsyncio", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.configure", "name": "configure", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "spec", "dimension", "metric", "timeout", "deletion_protection", "vector_type", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "spec", "dimension", "metric", "timeout", "deletion_protection", "vector_type", "tags"], "arg_types": ["pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "pinecone.db_control.models.serverless_spec.ServerlessSpec", "pinecone.db_control.models.pod_spec.PodSpec", "pinecone.db_control.models.byoc_spec.ByocSpec"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.metric.Metric", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.vector_type.VectorType", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of IndexResourceAsyncio", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pinecone.db_control.models.index_model.IndexModel"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.create", "name": "create", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "create_for_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 5, 5, 5], "arg_names": ["self", "name", "cloud", "region", "embed", "tags", "deletion_protection", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.create_for_model", "name": "create_for_model", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 5, 5, 5], "arg_names": ["self", "name", "cloud", "region", "embed", "tags", "deletion_protection", "timeout"], "arg_types": ["pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio", "builtins.str", {".class": "UnionType", "items": ["pinecone.db_control.enums.clouds.CloudProvider", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.clouds.AwsRegion", "pinecone.db_control.enums.clouds.GcpRegion", "pinecone.db_control.enums.clouds.AzureRegion", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.inference.models.index_embed.IndexEmbed", {".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_control.types.create_index_for_model_embed.CreateIndexForModelEmbedTypedDict"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_for_model of IndexResourceAsyncio", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pinecone.db_control.models.index_model.IndexModel"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.create_for_model", "name": "create_for_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "create_from_backup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5], "arg_names": ["self", "name", "backup_id", "deletion_protection", "tags", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.create_from_backup", "name": "create_from_backup", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5], "arg_names": ["self", "name", "backup_id", "deletion_protection", "tags", "timeout"], "arg_types": ["pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_from_backup of IndexResourceAsyncio", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pinecone.db_control.models.index_model.IndexModel"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.create_from_backup", "name": "create_from_backup", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["self", "name", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "name", "timeout"], "arg_types": ["pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of IndexResourceAsyncio", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.delete", "name": "delete", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "describe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.describe", "name": "describe", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "name"], "arg_types": ["pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe of IndexResourceAsyncio", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pinecone.db_control.models.index_model.IndexModel"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.describe", "name": "describe", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "has": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.has", "name": "has", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "name"], "arg_types": ["pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has of IndexResourceAsyncio", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.has", "name": "has", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.list", "name": "list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list of IndexResourceAsyncio", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pinecone.db_control.models.index_list.IndexList"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.list", "name": "list", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pinecone.db_control.resources.asyncio.index.IndexResourceAsyncio", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Metric": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.metric.Metric", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PineconeDBControlRequestFactory": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory", "kind": "Gdef"}, "PodSpec": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.pod_spec.PodSpec", "kind": "Gdef"}, "PodType": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.pod_type.PodType", "kind": "Gdef"}, "ServerlessSpec": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.serverless_spec.ServerlessSpec", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "VectorType": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.vector_type.VectorType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.asyncio.index.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.asyncio.index.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.asyncio.index.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.asyncio.index.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.asyncio.index.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.asyncio.index.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "docslinks": {".class": "SymbolTableNode", "cross_ref": "pinecone.utils.docslinks.docslinks", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pinecone.db_control.resources.asyncio.index.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "require_kwargs": {".class": "SymbolTableNode", "cross_ref": "pinecone.utils.require_kwargs.require_kwargs", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/pinecone/db_control/resources/asyncio/index.py"}