{"data_mtime": 1755541222, "dep_lines": [3, 5, 6, 6, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 20, 10, 5, 30, 30, 30, 30], "dependencies": ["pinecone.db_control.models", "pinecone.db_control.request_factory", "pinecone.utils.require_kwargs", "pinecone.utils", "logging", "builtins", "_frozen_importlib", "abc", "pinecone.db_control.models.collection_list", "typing"], "hash": "a1397a2de337478b335e347902fb790eedc5fe4c", "id": "pinecone.db_control.resources.asyncio.collection", "ignore_all": true, "interface_hash": "a9021b240be1bbccfd5ae90b1a47ea303afa0e01", "mtime": 1753678816, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/pinecone/db_control/resources/asyncio/collection.py", "plugin_data": null, "size": 1028, "suppressed": [], "version_id": "1.17.1"}