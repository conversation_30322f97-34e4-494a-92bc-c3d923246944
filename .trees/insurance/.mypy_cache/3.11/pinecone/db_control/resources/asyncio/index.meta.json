{"data_mtime": 1755541222, "dep_lines": [28, 30, 6, 14, 16, 26, 27, 29, 14, 1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 10, 20, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pinecone.core.openapi.db_control", "pinecone.db_control.types.configure_index_embed", "pinecone.db_control.models", "pinecone.utils.docslinks", "pinecone.db_control.enums", "pinecone.db_control.types", "pinecone.db_control.request_factory", "pinecone.utils.require_kwargs", "pinecone.utils", "logging", "asyncio", "typing", "builtins", "_frozen_importlib", "abc", "enum", "pinecone.db_control.enums.clouds", "pinecone.db_control.enums.deletion_protection", "pinecone.db_control.enums.metric", "pinecone.db_control.enums.pod_type", "pinecone.db_control.enums.vector_type", "pinecone.db_control.models.byoc_spec", "pinecone.db_control.models.index_list", "pinecone.db_control.models.index_model", "pinecone.db_control.models.pod_spec", "pinecone.db_control.models.serverless_spec", "pinecone.db_control.types.create_index_for_model_embed", "pinecone.inference", "pinecone.inference.inference_request_builder", "pinecone.inference.models", "pinecone.inference.models.index_embed"], "hash": "87fe52150dc18c391abe255cb3d5e68f1a923276", "id": "pinecone.db_control.resources.asyncio.index", "ignore_all": true, "interface_hash": "96519732df2f942a30327a358b168304f7f4e4f9", "mtime": 1753678816, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/pinecone/db_control/resources/asyncio/index.py", "plugin_data": null, "size": 6907, "suppressed": [], "version_id": "1.17.1"}