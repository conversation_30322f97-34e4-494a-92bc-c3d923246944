{".class": "MypyFile", "_fullname": "pinecone.db_control.resources.sync.index", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "API_VERSION": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.API_VERSION", "kind": "Gdef"}, "AwsRegion": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.AwsRegion", "kind": "Gdef"}, "AzureRegion": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.AzureRegion", "kind": "Gdef"}, "ByocSpec": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.byoc_spec.ByocSpec", "kind": "Gdef"}, "CloudProvider": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.CloudProvider", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "pinecone.config.config.Config", "kind": "Gdef"}, "ConfigureIndexEmbed": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.types.configure_index_embed.ConfigureIndexEmbed", "kind": "Gdef"}, "CreateIndexForModelEmbedTypedDict": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.types.create_index_for_model_embed.CreateIndexForModelEmbedTypedDict", "kind": "Gdef"}, "DeletionProtection": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.deletion_protection.DeletionProtection", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GcpRegion": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.GcpRegion", "kind": "Gdef"}, "IndexEmbed": {".class": "SymbolTableNode", "cross_ref": "pinecone.inference.models.index_embed.IndexEmbed", "kind": "Gdef"}, "IndexHostStore": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.index_host_store.IndexHostStore", "kind": "Gdef"}, "IndexList": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.index_list.IndexList", "kind": "Gdef"}, "IndexModel": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.index_model.IndexModel", "kind": "Gdef"}, "IndexResource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pinecone.utils.plugin_aware.PluginAware"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pinecone.db_control.resources.sync.index.IndexResource", "name": "IndexResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pinecone.db_control.resources.sync.index.IndexResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pinecone.db_control.resources.sync.index", "mro": ["pinecone.db_control.resources.sync.index.IndexResource", "pinecone.utils.plugin_aware.PluginAware", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index_api", "config", "openapi_config", "pool_threads"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index_api", "config", "openapi_config", "pool_threads"], "arg_types": ["pinecone.db_control.resources.sync.index.IndexResource", "pinecone.core.openapi.db_control.api.manage_indexes_api.ManageIndexesApi", {".class": "TypeAliasType", "args": [], "type_ref": "pinecone.config.config.Config"}, "pinecone.config.openapi_configuration.Configuration", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of IndexResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__poll_describe_index_until_ready": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.__poll_describe_index_until_ready", "name": "__poll_describe_index_until_ready", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "timeout"], "arg_types": ["pinecone.db_control.resources.sync.index.IndexResource", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__poll_describe_index_until_ready of IndexResource", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource._get_host", "name": "_get_host", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pinecone.db_control.resources.sync.index.IndexResource", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_host of IndexResource", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_index_api": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource._index_api", "name": "_index_api", "setter_type": null, "type": "pinecone.core.openapi.db_control.api.manage_indexes_api.ManageIndexesApi"}}, "_index_host_store": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource._index_host_store", "name": "_index_host_store", "setter_type": null, "type": "pinecone.db_control.index_host_store.IndexHostStore"}}, "_openapi_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource._openapi_config", "name": "_openapi_config", "setter_type": null, "type": "pinecone.config.openapi_configuration.Configuration"}}, "_pool_threads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource._pool_threads", "name": "_pool_threads", "setter_type": null, "type": "builtins.int"}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.config", "name": "config", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pinecone.config.config.Config"}}}, "configure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "replicas", "pod_type", "deletion_protection", "tags", "embed"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.configure", "name": "configure", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "replicas", "pod_type", "deletion_protection", "tags", "embed"], "arg_types": ["pinecone.db_control.resources.sync.index.IndexResource", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.pod_type.PodType", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_control.types.configure_index_embed.ConfigureIndexEmbed"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "configure of IndexResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.configure", "name": "configure", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "spec", "dimension", "metric", "timeout", "deletion_protection", "vector_type", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "spec", "dimension", "metric", "timeout", "deletion_protection", "vector_type", "tags"], "arg_types": ["pinecone.db_control.resources.sync.index.IndexResource", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "pinecone.db_control.models.serverless_spec.ServerlessSpec", "pinecone.db_control.models.pod_spec.PodSpec", "pinecone.db_control.models.byoc_spec.ByocSpec"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.metric.Metric", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.vector_type.VectorType", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of IndexResource", "ret_type": "pinecone.db_control.models.index_model.IndexModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.create", "name": "create", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "create_for_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 5, 5, 5], "arg_names": ["self", "name", "cloud", "region", "embed", "tags", "deletion_protection", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.create_for_model", "name": "create_for_model", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 5, 5, 5], "arg_names": ["self", "name", "cloud", "region", "embed", "tags", "deletion_protection", "timeout"], "arg_types": ["pinecone.db_control.resources.sync.index.IndexResource", "builtins.str", {".class": "UnionType", "items": ["pinecone.db_control.enums.clouds.CloudProvider", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.clouds.AwsRegion", "pinecone.db_control.enums.clouds.GcpRegion", "pinecone.db_control.enums.clouds.AzureRegion", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.inference.models.index_embed.IndexEmbed", {".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_control.types.create_index_for_model_embed.CreateIndexForModelEmbedTypedDict"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_for_model of IndexResource", "ret_type": "pinecone.db_control.models.index_model.IndexModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.create_for_model", "name": "create_for_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "create_from_backup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5], "arg_names": ["self", "name", "backup_id", "deletion_protection", "tags", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.create_from_backup", "name": "create_from_backup", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5], "arg_names": ["self", "name", "backup_id", "deletion_protection", "tags", "timeout"], "arg_types": ["pinecone.db_control.resources.sync.index.IndexResource", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_from_backup of IndexResource", "ret_type": "pinecone.db_control.models.index_model.IndexModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.create_from_backup", "name": "create_from_backup", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["self", "name", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "name", "timeout"], "arg_types": ["pinecone.db_control.resources.sync.index.IndexResource", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of IndexResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.delete", "name": "delete", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "describe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.describe", "name": "describe", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "name"], "arg_types": ["pinecone.db_control.resources.sync.index.IndexResource", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe of IndexResource", "ret_type": "pinecone.db_control.models.index_model.IndexModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.describe", "name": "describe", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "has": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.has", "name": "has", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "name"], "arg_types": ["pinecone.db_control.resources.sync.index.IndexResource", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has of IndexResource", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.has", "name": "has", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.list", "name": "list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pinecone.db_control.resources.sync.index.IndexResource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list of IndexResource", "ret_type": "pinecone.db_control.models.index_list.IndexList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.sync.index.IndexResource.list", "name": "list", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pinecone.db_control.resources.sync.index.IndexResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pinecone.db_control.resources.sync.index.IndexResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ManageIndexesApi": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.api.manage_indexes_api.ManageIndexesApi", "kind": "Gdef"}, "Metric": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.metric.Metric", "kind": "Gdef"}, "OpenApiConfiguration": {".class": "SymbolTableNode", "cross_ref": "pinecone.config.openapi_configuration.Configuration", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PineconeDBControlRequestFactory": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory", "kind": "Gdef"}, "PluginAware": {".class": "SymbolTableNode", "cross_ref": "pinecone.utils.plugin_aware.PluginAware", "kind": "Gdef"}, "PodSpec": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.pod_spec.PodSpec", "kind": "Gdef"}, "PodType": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.pod_type.PodType", "kind": "Gdef"}, "ServerlessSpec": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.serverless_spec.ServerlessSpec", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "VectorType": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.vector_type.VectorType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.sync.index.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.sync.index.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.sync.index.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.sync.index.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.sync.index.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.sync.index.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "docslinks": {".class": "SymbolTableNode", "cross_ref": "pinecone.utils.docslinks.docslinks", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pinecone.db_control.resources.sync.index.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "require_kwargs": {".class": "SymbolTableNode", "cross_ref": "pinecone.utils.require_kwargs.require_kwargs", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/pinecone/db_control/resources/sync/index.py"}