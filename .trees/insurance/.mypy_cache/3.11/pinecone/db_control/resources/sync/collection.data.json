{".class": "MypyFile", "_fullname": "pinecone.db_control.resources.sync.collection", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CollectionList": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.collection_list.CollectionList", "kind": "Gdef"}, "CollectionResource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pinecone.utils.plugin_aware.PluginAware"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource", "name": "CollectionResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pinecone.db_control.resources.sync.collection", "mro": ["pinecone.db_control.resources.sync.collection.CollectionResource", "pinecone.utils.plugin_aware.PluginAware", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index_api", "config", "openapi_config", "pool_threads"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index_api", "config", "openapi_config", "pool_threads"], "arg_types": ["pinecone.db_control.resources.sync.collection.CollectionResource", "pinecone.core.openapi.db_control.api.manage_indexes_api.ManageIndexesApi", {".class": "TypeAliasType", "args": [], "type_ref": "pinecone.config.config.Config"}, "pinecone.config.openapi_configuration.Configuration", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CollectionResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_openapi_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource._openapi_config", "name": "_openapi_config", "setter_type": null, "type": "pinecone.config.openapi_configuration.Configuration"}}, "_pool_threads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource._pool_threads", "name": "_pool_threads", "setter_type": null, "type": "builtins.int"}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource.config", "name": "config", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pinecone.config.config.Config"}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["self", "name", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["self", "name", "source"], "arg_types": ["pinecone.db_control.resources.sync.collection.CollectionResource", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of CollectionResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource.create", "name": "create", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "name"], "arg_types": ["pinecone.db_control.resources.sync.collection.CollectionResource", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of CollectionResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource.delete", "name": "delete", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "describe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource.describe", "name": "describe", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "name"], "arg_types": ["pinecone.db_control.resources.sync.collection.CollectionResource", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "describe of CollectionResource", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource.describe", "name": "describe", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "index_api": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource.index_api", "name": "index_api", "setter_type": null, "type": "pinecone.core.openapi.db_control.api.manage_indexes_api.ManageIndexesApi"}}, "list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource.list", "name": "list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pinecone.db_control.resources.sync.collection.CollectionResource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list of CollectionResource", "ret_type": "pinecone.db_control.models.collection_list.CollectionList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource.list", "name": "list", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pinecone.db_control.resources.sync.collection.CollectionResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pinecone.db_control.resources.sync.collection.CollectionResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Config": {".class": "SymbolTableNode", "cross_ref": "pinecone.config.config.Config", "kind": "Gdef"}, "ManageIndexesApi": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.api.manage_indexes_api.ManageIndexesApi", "kind": "Gdef"}, "OpenApiConfiguration": {".class": "SymbolTableNode", "cross_ref": "pinecone.config.openapi_configuration.Configuration", "kind": "Gdef"}, "PineconeDBControlRequestFactory": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory", "kind": "Gdef"}, "PluginAware": {".class": "SymbolTableNode", "cross_ref": "pinecone.utils.plugin_aware.PluginAware", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.sync.collection.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.sync.collection.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.sync.collection.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.sync.collection.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.sync.collection.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.resources.sync.collection.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pinecone.db_control.resources.sync.collection.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "require_kwargs": {".class": "SymbolTableNode", "cross_ref": "pinecone.utils.require_kwargs.require_kwargs", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/pinecone/db_control/resources/sync/collection.py"}