{"data_mtime": 1755541222, "dep_lines": [12, 4, 5, 6, 6, 13, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 10, 5, 25, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pinecone.core.openapi.db_control.api.manage_indexes_api", "pinecone.db_control.models", "pinecone.db_control.request_factory", "pinecone.utils.require_kwargs", "pinecone.utils", "pinecone.config", "typing", "logging", "builtins", "_frozen_importlib", "abc", "pinecone.config.config", "pinecone.config.openapi_configuration", "pinecone.core", "pinecone.core.openapi", "pinecone.core.openapi.db_control", "pinecone.core.openapi.db_control.api", "pinecone.db_control.models.collection_list", "pinecone.utils.plugin_aware"], "hash": "33ed399c7f74c31e606ec2498f7b9f262fa54d4c", "id": "pinecone.db_control.resources.sync.collection", "ignore_all": true, "interface_hash": "f0468f7e41159c0f6b77927a01bd4c605ac48ae9", "mtime": 1753678816, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/pinecone/db_control/resources/sync/collection.py", "plugin_data": null, "size": 1658, "suppressed": [], "version_id": "1.17.1"}