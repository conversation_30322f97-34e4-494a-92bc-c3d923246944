{".class": "MypyFile", "_fullname": "pinecone.db_control.request_factory", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AwsRegion": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.AwsRegion", "kind": "Gdef"}, "AzureRegion": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.AzureRegion", "kind": "Gdef"}, "ByocSpec": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.byoc_spec.ByocSpec", "kind": "Gdef"}, "ByocSpecModel": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.byoc_spec.ByocSpec", "kind": "Gdef"}, "CloudProvider": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.CloudProvider", "kind": "Gdef"}, "ConfigureIndexEmbed": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.types.configure_index_embed.ConfigureIndexEmbed", "kind": "Gdef"}, "ConfigureIndexRequest": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.configure_index_request.ConfigureIndexRequest", "kind": "Gdef"}, "ConfigureIndexRequestEmbed": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.configure_index_request_embed.ConfigureIndexRequestEmbed", "kind": "Gdef"}, "ConfigureIndexRequestSpec": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.configure_index_request_spec.ConfigureIndexRequestSpec", "kind": "Gdef"}, "ConfigureIndexRequestSpecPod": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.configure_index_request_spec_pod.ConfigureIndexRequestSpecPod", "kind": "Gdef"}, "CreateCollectionRequest": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.create_collection_request.CreateCollectionRequest", "kind": "Gdef"}, "CreateIndexForModelEmbedTypedDict": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.types.create_index_for_model_embed.CreateIndexForModelEmbedTypedDict", "kind": "Gdef"}, "CreateIndexForModelRequest": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.create_index_for_model_request.CreateIndexForModelRequest", "kind": "Gdef"}, "CreateIndexForModelRequestEmbed": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.create_index_for_model_request_embed.CreateIndexForModelRequestEmbed", "kind": "Gdef"}, "CreateIndexFromBackupRequest": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.create_index_from_backup_request.CreateIndexFromBackupRequest", "kind": "Gdef"}, "CreateIndexRequest": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.create_index_request.CreateIndexRequest", "kind": "Gdef"}, "DeletionProtection": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.deletion_protection.DeletionProtection", "kind": "Gdef"}, "DeletionProtectionModel": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.deletion_protection.DeletionProtection", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "GcpRegion": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.clouds.GcpRegion", "kind": "Gdef"}, "IndexEmbed": {".class": "SymbolTableNode", "cross_ref": "pinecone.inference.models.index_embed.IndexEmbed", "kind": "Gdef"}, "IndexModel": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.index_model.IndexModel", "kind": "Gdef"}, "IndexSpec": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.index_spec.IndexSpec", "kind": "Gdef"}, "IndexTags": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.index_tags.IndexTags", "kind": "Gdef"}, "Metric": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.metric.Metric", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PineconeDBControlRequestFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory", "name": "PineconeDBControlRequestFactory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pinecone.db_control.request_factory", "mro": ["pinecone.db_control.request_factory.PineconeDBControlRequestFactory", "builtins.object"], "names": {".class": "SymbolTable", "__parse_deletion_protection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["deletion_protection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.__parse_deletion_protection", "name": "__parse_deletion_protection", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["deletion_protection"], "arg_types": [{".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__parse_deletion_protection of PineconeDBControlRequestFactory", "ret_type": "pinecone.core.openapi.db_control.model.deletion_protection.DeletionProtection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.__parse_deletion_protection", "name": "__parse_deletion_protection", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["deletion_protection"], "arg_types": [{".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__parse_deletion_protection of PineconeDBControlRequestFactory", "ret_type": "pinecone.core.openapi.db_control.model.deletion_protection.DeletionProtection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__parse_index_spec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["spec"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.__parse_index_spec", "name": "__parse_index_spec", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["spec"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "pinecone.db_control.models.serverless_spec.ServerlessSpec", "pinecone.db_control.models.pod_spec.PodSpec", "pinecone.db_control.models.byoc_spec.ByocSpec"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__parse_index_spec of PineconeDBControlRequestFactory", "ret_type": "pinecone.core.openapi.db_control.model.index_spec.IndexSpec", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.__parse_index_spec", "name": "__parse_index_spec", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["spec"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "pinecone.db_control.models.serverless_spec.ServerlessSpec", "pinecone.db_control.models.pod_spec.PodSpec", "pinecone.db_control.models.byoc_spec.ByocSpec"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__parse_index_spec of PineconeDBControlRequestFactory", "ret_type": "pinecone.core.openapi.db_control.model.index_spec.IndexSpec", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__parse_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.__parse_tags", "name": "__parse_tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tags"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__parse_tags of PineconeDBControlRequestFactory", "ret_type": "pinecone.core.openapi.db_control.model.index_tags.IndexTags", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.__parse_tags", "name": "__parse_tags", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tags"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__parse_tags of PineconeDBControlRequestFactory", "ret_type": "pinecone.core.openapi.db_control.model.index_tags.IndexTags", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "configure_index_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["description", "replicas", "pod_type", "deletion_protection", "tags", "embed"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.configure_index_request", "name": "configure_index_request", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["description", "replicas", "pod_type", "deletion_protection", "tags", "embed"], "arg_types": ["pinecone.db_control.models.index_model.IndexModel", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.pod_type.PodType", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_control.types.configure_index_embed.ConfigureIndexEmbed"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "configure_index_request of PineconeDBControlRequestFactory", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.configure_index_request", "name": "configure_index_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["description", "replicas", "pod_type", "deletion_protection", "tags", "embed"], "arg_types": ["pinecone.db_control.models.index_model.IndexModel", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.pod_type.PodType", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_control.types.configure_index_embed.ConfigureIndexEmbed"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "configure_index_request of PineconeDBControlRequestFactory", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_collection_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["name", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.create_collection_request", "name": "create_collection_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["name", "source"], "arg_types": ["builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_collection_request of PineconeDBControlRequestFactory", "ret_type": "pinecone.core.openapi.db_control.model.create_collection_request.CreateCollectionRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.create_collection_request", "name": "create_collection_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["name", "source"], "arg_types": ["builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_collection_request of PineconeDBControlRequestFactory", "ret_type": "pinecone.core.openapi.db_control.model.create_collection_request.CreateCollectionRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_index_for_model_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["name", "cloud", "region", "embed", "tags", "deletion_protection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.create_index_for_model_request", "name": "create_index_for_model_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["name", "cloud", "region", "embed", "tags", "deletion_protection"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["pinecone.db_control.enums.clouds.CloudProvider", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.clouds.AwsRegion", "pinecone.db_control.enums.clouds.GcpRegion", "pinecone.db_control.enums.clouds.AzureRegion", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.inference.models.index_embed.IndexEmbed", {".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_control.types.create_index_for_model_embed.CreateIndexForModelEmbedTypedDict"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_index_for_model_request of PineconeDBControlRequestFactory", "ret_type": "pinecone.core.openapi.db_control.model.create_index_for_model_request.CreateIndexForModelRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.create_index_for_model_request", "name": "create_index_for_model_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["name", "cloud", "region", "embed", "tags", "deletion_protection"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["pinecone.db_control.enums.clouds.CloudProvider", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.clouds.AwsRegion", "pinecone.db_control.enums.clouds.GcpRegion", "pinecone.db_control.enums.clouds.AzureRegion", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.inference.models.index_embed.IndexEmbed", {".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_control.types.create_index_for_model_embed.CreateIndexForModelEmbedTypedDict"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_index_for_model_request of PineconeDBControlRequestFactory", "ret_type": "pinecone.core.openapi.db_control.model.create_index_for_model_request.CreateIndexForModelRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_index_from_backup_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["name", "deletion_protection", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.create_index_from_backup_request", "name": "create_index_from_backup_request", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["name", "deletion_protection", "tags"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_index_from_backup_request of PineconeDBControlRequestFactory", "ret_type": "pinecone.core.openapi.db_control.model.create_index_from_backup_request.CreateIndexFromBackupRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.create_index_from_backup_request", "name": "create_index_from_backup_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["name", "deletion_protection", "tags"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_index_from_backup_request of PineconeDBControlRequestFactory", "ret_type": "pinecone.core.openapi.db_control.model.create_index_from_backup_request.CreateIndexFromBackupRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_index_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["name", "spec", "dimension", "metric", "deletion_protection", "vector_type", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.create_index_request", "name": "create_index_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["name", "spec", "dimension", "metric", "deletion_protection", "vector_type", "tags"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "pinecone.db_control.models.serverless_spec.ServerlessSpec", "pinecone.db_control.models.pod_spec.PodSpec", "pinecone.db_control.models.byoc_spec.ByocSpec"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.metric.Metric", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.vector_type.VectorType", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_index_request of PineconeDBControlRequestFactory", "ret_type": "pinecone.core.openapi.db_control.model.create_index_request.CreateIndexRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.create_index_request", "name": "create_index_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["name", "spec", "dimension", "metric", "deletion_protection", "vector_type", "tags"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "pinecone.db_control.models.serverless_spec.ServerlessSpec", "pinecone.db_control.models.pod_spec.PodSpec", "pinecone.db_control.models.byoc_spec.ByocSpec"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.metric.Metric", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.deletion_protection.DeletionProtection", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_control.enums.vector_type.VectorType", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_index_request of PineconeDBControlRequestFactory", "ret_type": "pinecone.core.openapi.db_control.model.create_index_request.CreateIndexRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pinecone.db_control.request_factory.PineconeDBControlRequestFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PodSpec": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.pod_spec.PodSpec", "kind": "Gdef"}, "PodSpecMetadataConfig": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.pod_spec_metadata_config.PodSpecMetadataConfig", "kind": "Gdef"}, "PodSpecModel": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.pod_spec.PodSpec", "kind": "Gdef"}, "PodType": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.pod_type.PodType", "kind": "Gdef"}, "ServerlessSpec": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.models.serverless_spec.ServerlessSpec", "kind": "Gdef"}, "ServerlessSpecModel": {".class": "SymbolTableNode", "cross_ref": "pinecone.core.openapi.db_control.model.serverless_spec.ServerlessSpec", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "VectorType": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_control.enums.vector_type.VectorType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.request_factory.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.request_factory.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.request_factory.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.request_factory.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.request_factory.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_control.request_factory.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "convert_enum_to_string": {".class": "SymbolTableNode", "cross_ref": "pinecone.utils.convert_enum_to_string.convert_enum_to_string", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pinecone.db_control.request_factory.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "parse_non_empty_args": {".class": "SymbolTableNode", "cross_ref": "pinecone.utils.parse_args.parse_non_empty_args", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/pinecone/db_control/request_factory.py"}