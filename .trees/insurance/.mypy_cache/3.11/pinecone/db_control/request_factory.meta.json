{"data_mtime": 1755541222, "dep_lines": [7, 8, 11, 14, 15, 16, 19, 22, 25, 28, 29, 30, 33, 34, 35, 36, 5, 39, 41, 51, 5, 1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pinecone.core.openapi.db_control.model.create_collection_request", "pinecone.core.openapi.db_control.model.create_index_for_model_request", "pinecone.core.openapi.db_control.model.create_index_for_model_request_embed", "pinecone.core.openapi.db_control.model.create_index_request", "pinecone.core.openapi.db_control.model.configure_index_request", "pinecone.core.openapi.db_control.model.configure_index_request_spec", "pinecone.core.openapi.db_control.model.configure_index_request_spec_pod", "pinecone.core.openapi.db_control.model.configure_index_request_embed", "pinecone.core.openapi.db_control.model.deletion_protection", "pinecone.core.openapi.db_control.model.index_spec", "pinecone.core.openapi.db_control.model.index_tags", "pinecone.core.openapi.db_control.model.serverless_spec", "pinecone.core.openapi.db_control.model.byoc_spec", "pinecone.core.openapi.db_control.model.pod_spec", "pinecone.core.openapi.db_control.model.pod_spec_metadata_config", "pinecone.core.openapi.db_control.model.create_index_from_backup_request", "pinecone.utils.convert_enum_to_string", "pinecone.db_control.models", "pinecone.db_control.enums", "pinecone.db_control.types", "pinecone.utils", "logging", "typing", "enum", "builtins", "_frozen_importlib", "abc", "pinecone.core", "pinecone.core.openapi", "pinecone.core.openapi.db_control", "pinecone.core.openapi.db_control.model", "pinecone.db_control.enums.clouds", "pinecone.db_control.enums.deletion_protection", "pinecone.db_control.enums.metric", "pinecone.db_control.enums.pod_type", "pinecone.db_control.enums.vector_type", "pinecone.db_control.models.byoc_spec", "pinecone.db_control.models.index_model", "pinecone.db_control.models.pod_spec", "pinecone.db_control.models.serverless_spec", "pinecone.db_control.types.configure_index_embed", "pinecone.db_control.types.create_index_for_model_embed", "pinecone.inference", "pinecone.inference.inference_request_builder", "pinecone.inference.models", "pinecone.inference.models.index_embed", "pinecone.openapi_support", "pinecone.openapi_support.model_utils"], "hash": "cca6711ca96bc2f027090a48bfcbf31d4664fb1e", "id": "pinecone.db_control.request_factory", "ignore_all": true, "interface_hash": "b4433a6ecc57589b7980b7ef989b65f9de1dd759", "mtime": 1753678816, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/pinecone/db_control/request_factory.py", "plugin_data": null, "size": 12142, "suppressed": [], "version_id": "1.17.1"}