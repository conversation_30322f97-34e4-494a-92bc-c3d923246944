{".class": "MypyFile", "_fullname": "pinecone.db_data.dataclasses.vector", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DictLike": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_data.dataclasses.utils.DictLike", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SparseValues": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_data.dataclasses.sparse_values.SparseValues", "kind": "Gdef"}, "Vector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pinecone.db_data.dataclasses.utils.DictLike"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pinecone.db_data.dataclasses.vector.Vector", "name": "Vector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pinecone.db_data.dataclasses.vector.Vector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 11, "name": "id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 12, "name": "values", "type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 13, "name": "metadata", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_data.types.vector_metadata_dict.VectorMetadataTypedDict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 14, "name": "sparse_values", "type": {".class": "UnionType", "items": ["pinecone.db_data.dataclasses.sparse_values.SparseValues", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pinecone.db_data.dataclasses.vector", "mro": ["pinecone.db_data.dataclasses.vector.Vector", "pinecone.db_data.dataclasses.utils.DictLike", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pinecone.db_data.dataclasses.vector.Vector.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "id", "values", "metadata", "sparse_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pinecone.db_data.dataclasses.vector.Vector.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "id", "values", "metadata", "sparse_values"], "arg_types": ["pinecone.db_data.dataclasses.vector.Vector", "builtins.str", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_data.types.vector_metadata_dict.VectorMetadataTypedDict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_data.dataclasses.sparse_values.SparseValues", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Vector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pinecone.db_data.dataclasses.vector.Vector.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "values"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "metadata"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sparse_values"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pinecone.db_data.dataclasses.vector.Vector.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pinecone.db_data.dataclasses.vector.Vector"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-post_init of Vector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["id", "values", "metadata", "sparse_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pinecone.db_data.dataclasses.vector.Vector.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["id", "values", "metadata", "sparse_values"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_data.types.vector_metadata_dict.VectorMetadataTypedDict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_data.dataclasses.sparse_values.SparseValues", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Vector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pinecone.db_data.dataclasses.vector.Vector.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["id", "values", "metadata", "sparse_values"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_data.types.vector_metadata_dict.VectorMetadataTypedDict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["pinecone.db_data.dataclasses.sparse_values.SparseValues", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Vector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pinecone.db_data.dataclasses.vector.Vector.__post_init__", "name": "__post_init__", "type": null}}, "from_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["vector_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pinecone.db_data.dataclasses.vector.Vector.from_dict", "name": "from_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["vector_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_data.types.vector_typed_dict.VectorTypedDict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_dict of Vector", "ret_type": "pinecone.db_data.dataclasses.vector.Vector", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pinecone.db_data.dataclasses.vector.Vector.from_dict", "name": "from_dict", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["vector_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_data.types.vector_typed_dict.VectorTypedDict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_dict of Vector", "ret_type": "pinecone.db_data.dataclasses.vector.Vector", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pinecone.db_data.dataclasses.vector.Vector.id", "name": "id", "setter_type": null, "type": "builtins.str"}}, "metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pinecone.db_data.dataclasses.vector.Vector.metadata", "name": "metadata", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_data.types.vector_metadata_dict.VectorMetadataTypedDict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "sparse_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pinecone.db_data.dataclasses.vector.Vector.sparse_values", "name": "sparse_values", "setter_type": null, "type": {".class": "UnionType", "items": ["pinecone.db_data.dataclasses.sparse_values.SparseValues", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pinecone.db_data.dataclasses.vector.Vector.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pinecone.db_data.dataclasses.vector.Vector"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_dict of Vector", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pinecone.db_data.types.vector_typed_dict.VectorTypedDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pinecone.db_data.dataclasses.vector.Vector.values", "name": "values", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pinecone.db_data.dataclasses.vector.Vector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pinecone.db_data.dataclasses.vector.Vector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VectorMetadataTypedDict": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_data.types.vector_metadata_dict.VectorMetadataTypedDict", "kind": "Gdef"}, "VectorTypedDict": {".class": "SymbolTableNode", "cross_ref": "pinecone.db_data.types.vector_typed_dict.VectorTypedDict", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_data.dataclasses.vector.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_data.dataclasses.vector.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_data.dataclasses.vector.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_data.dataclasses.vector.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_data.dataclasses.vector.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pinecone.db_data.dataclasses.vector.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/pinecone/db_data/dataclasses/vector.py"}