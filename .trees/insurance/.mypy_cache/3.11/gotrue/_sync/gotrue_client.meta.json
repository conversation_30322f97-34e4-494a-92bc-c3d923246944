{"data_mtime": 1755541222, "dep_lines": [88, 89, 90, 91, 8, 13, 21, 29, 44, 45, 46, 1, 3, 4, 5, 6, 7, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["gotrue._sync.gotrue_admin_api", "gotrue._sync.gotrue_base_api", "gotrue._sync.gotrue_mfa_api", "gotrue._sync.storage", "urllib.parse", "gotrue.constants", "gotrue.errors", "gotrue.helpers", "gotrue.http_clients", "gotrue.timer", "gotrue.types", "__future__", "time", "contextlib", "functools", "json", "typing", "uuid", "jwt", "builtins", "_frozen_importlib", "_typeshed", "abc", "httpx", "httpx._client", "httpx._models", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types"], "hash": "7ab67d1a39e51685152ad33671cff1f9f85a79ac", "id": "gotrue._sync.gotrue_client", "ignore_all": true, "interface_hash": "67f3a8321cba0344fb3b20fd51f672bfbe6d6641", "mtime": 1753678820, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/gotrue/_sync/gotrue_client.py", "plugin_data": null, "size": 44398, "suppressed": [], "version_id": "1.17.1"}