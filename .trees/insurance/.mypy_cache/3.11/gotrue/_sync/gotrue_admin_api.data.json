{".class": "MypyFile", "_fullname": "gotrue._sync.gotrue_admin_api", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AdminUserAttributes": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AdminUserAttributes", "kind": "Gdef"}, "AuthMFAAdminDeleteFactorParams": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthMFAAdminDeleteFactorParams", "kind": "Gdef"}, "AuthMFAAdminDeleteFactorResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthMFAAdminDeleteFactorResponse", "kind": "Gdef"}, "AuthMFAAdminListFactorsParams": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthMFAAdminListFactorsParams", "kind": "Gdef"}, "AuthMFAAdminListFactorsResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthMFAAdminListFactorsResponse", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GenerateLinkParams": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.GenerateLinkParams", "kind": "Gdef"}, "GenerateLinkResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.GenerateLinkResponse", "kind": "Gdef"}, "InviteUserByEmailOptions": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.InviteUserByEmailOptions", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SignOutScope": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.SignOutScope", "kind": "Gdef"}, "SyncClient": {".class": "SymbolTableNode", "cross_ref": "gotrue.http_clients.SyncClient", "kind": "Gdef"}, "SyncGoTrueAdminAPI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["gotrue._sync.gotrue_base_api.SyncGoTrueBaseAPI"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", "name": "SyncGoTrueAdminAPI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "gotrue._sync.gotrue_admin_api", "mro": ["gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", "gotrue._sync.gotrue_base_api.SyncGoTrueBaseAPI", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "url", "headers", "http_client", "verify", "proxy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "url", "headers", "http_client", "verify", "proxy"], "arg_types": ["gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["gotrue.http_clients.SyncClient", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SyncGoTrueAdminAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_delete_factor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI._delete_factor", "name": "_delete_factor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.AuthMFAAdminDeleteFactorParams"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_delete_factor of SyncGoTrueAdminAPI", "ret_type": "gotrue.types.AuthMFAAdminDeleteFactorResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_list_factors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI._list_factors", "name": "_list_factors", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.AuthMFAAdminListFactorsParams"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_list_factors of SyncGoTrueAdminAPI", "ret_type": "gotrue.types.AuthMFAAdminListFactorsResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_uuid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI._validate_uuid", "name": "_validate_uuid", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "id"], "arg_types": ["gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_uuid of SyncGoTrueAdminAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI.create_user", "name": "create_user", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attributes"], "arg_types": ["gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.AdminUserAttributes"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_user of SyncGoTrueAdminAPI", "ret_type": "gotrue.types.UserResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "id", "should_soft_delete"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI.delete_user", "name": "delete_user", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "id", "should_soft_delete"], "arg_types": ["gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", "builtins.str", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_user of SyncGoTrueAdminAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_link": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI.generate_link", "name": "generate_link", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.GenerateLinkParams"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate_link of SyncGoTrueAdminAPI", "ret_type": "gotrue.types.GenerateLinkResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_user_by_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI.get_user_by_id", "name": "get_user_by_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uid"], "arg_types": ["gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_user_by_id of SyncGoTrueAdminAPI", "ret_type": "gotrue.types.UserResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "invite_user_by_email": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "email", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI.invite_user_by_email", "name": "invite_user_by_email", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "email", "options"], "arg_types": ["gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.InviteUserByEmailOptions"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "invite_user_by_email of SyncGoTrueAdminAPI", "ret_type": "gotrue.types.UserResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_users": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "page", "per_page"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI.list_users", "name": "list_users", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "page", "per_page"], "arg_types": ["gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_users of SyncGoTrueAdminAPI", "ret_type": {".class": "Instance", "args": ["gotrue.types.User"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mfa": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI.mfa", "name": "mfa", "setter_type": null, "type": "gotrue._sync.gotrue_admin_mfa_api.SyncGoTrueAdminMFAAPI"}}, "sign_out": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "jwt", "scope"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI.sign_out", "name": "sign_out", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "jwt", "scope"], "arg_types": ["gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.SignOutScope"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sign_out of SyncGoTrueAdminAPI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_user_by_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "uid", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI.update_user_by_id", "name": "update_user_by_id", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "uid", "attributes"], "arg_types": ["gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.AdminUserAttributes"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_user_by_id of SyncGoTrueAdminAPI", "ret_type": "gotrue.types.UserResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SyncGoTrueAdminMFAAPI": {".class": "SymbolTableNode", "cross_ref": "gotrue._sync.gotrue_admin_mfa_api.SyncGoTrueAdminMFAAPI", "kind": "Gdef"}, "SyncGoTrueBaseAPI": {".class": "SymbolTableNode", "cross_ref": "gotrue._sync.gotrue_base_api.SyncGoTrueBaseAPI", "kind": "Gdef"}, "User": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.User", "kind": "Gdef"}, "UserResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.UserResponse", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gotrue._sync.gotrue_admin_api.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gotrue._sync.gotrue_admin_api.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gotrue._sync.gotrue_admin_api.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gotrue._sync.gotrue_admin_api.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gotrue._sync.gotrue_admin_api.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gotrue._sync.gotrue_admin_api.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "is_valid_uuid": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.is_valid_uuid", "kind": "Gdef"}, "model_validate": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.model_validate", "kind": "Gdef"}, "parse_link_response": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.parse_link_response", "kind": "Gdef"}, "parse_user_response": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.parse_user_response", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/gotrue/_sync/gotrue_admin_api.py"}