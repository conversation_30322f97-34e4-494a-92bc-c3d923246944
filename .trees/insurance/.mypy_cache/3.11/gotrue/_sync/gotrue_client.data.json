{".class": "MypyFile", "_fullname": "gotrue._sync.gotrue_client", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AuthApiError": {".class": "SymbolTableNode", "cross_ref": "gotrue.errors.Auth<PERSON><PERSON><PERSON><PERSON>r", "kind": "Gdef"}, "AuthChangeEvent": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthChangeEvent", "kind": "Gdef"}, "AuthFlowType": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthFlowType", "kind": "Gdef"}, "AuthImplicitGrantRedirectError": {".class": "SymbolTableNode", "cross_ref": "gotrue.errors.AuthImplicitGrantRedirectError", "kind": "Gdef"}, "AuthInvalidCredentialsError": {".class": "SymbolTableNode", "cross_ref": "gotrue.errors.AuthInvalidCredentialsError", "kind": "Gdef"}, "AuthInvalidJwtError": {".class": "SymbolTableNode", "cross_ref": "gotrue.errors.AuthInvalidJwtError", "kind": "Gdef"}, "AuthMFAChallengeResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthMFAChallengeResponse", "kind": "Gdef"}, "AuthMFAEnrollResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthMFAEnrollResponse", "kind": "Gdef"}, "AuthMFAGetAuthenticatorAssuranceLevelResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthMFAGetAuthenticatorAssuranceLevelResponse", "kind": "Gdef"}, "AuthMFAListFactorsResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthMFAListFactorsResponse", "kind": "Gdef"}, "AuthMFAUnenrollResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthMFAUnenrollResponse", "kind": "Gdef"}, "AuthMFAVerifyResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthMFAVerifyResponse", "kind": "Gdef"}, "AuthOtpResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthOtpResponse", "kind": "Gdef"}, "AuthResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthResponse", "kind": "Gdef"}, "AuthRetryableError": {".class": "SymbolTableNode", "cross_ref": "gotrue.errors.AuthRetryableError", "kind": "Gdef"}, "AuthSessionMissingError": {".class": "SymbolTableNode", "cross_ref": "gotrue.errors.AuthSessionMissingError", "kind": "Gdef"}, "AuthenticatorAssuranceLevels": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.AuthenticatorAssuranceLevels", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClaimsResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.ClaimsResponse", "kind": "Gdef"}, "CodeExchangeParams": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.CodeExchangeParams", "kind": "Gdef"}, "DEFAULT_HEADERS": {".class": "SymbolTableNode", "cross_ref": "gotrue.constants.DEFAULT_HEADERS", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EXPIRY_MARGIN": {".class": "SymbolTableNode", "cross_ref": "gotrue.constants.EXPIRY_MARGIN", "kind": "Gdef"}, "GOTRUE_URL": {".class": "SymbolTableNode", "cross_ref": "gotrue.constants.GOTRUE_URL", "kind": "Gdef"}, "IdentitiesResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.IdentitiesResponse", "kind": "Gdef"}, "JWK": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.JWK", "kind": "Gdef"}, "JWKSet": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.JWKSet", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MAX_RETRIES": {".class": "SymbolTableNode", "cross_ref": "gotrue.constants.MAX_RETRIES", "kind": "Gdef"}, "MFAChallengeAndVerifyParams": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.MFAChallengeAndVerifyParams", "kind": "Gdef"}, "MFAChallengeParams": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.MFAChallengeParams", "kind": "Gdef"}, "MFAEnrollParams": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.MFAEnrollParams", "kind": "Gdef"}, "MFAUnenrollParams": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.MFAUnenrollParams", "kind": "Gdef"}, "MFAVerifyParams": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.MFAVerifyParams", "kind": "Gdef"}, "OAuthResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.OAuthResponse", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Options": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.Options", "kind": "Gdef"}, "Provider": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.Provider", "kind": "Gdef"}, "RETRY_INTERVAL": {".class": "SymbolTableNode", "cross_ref": "gotrue.constants.RETRY_INTERVAL", "kind": "Gdef"}, "ResendCredentials": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.ResendCredentials", "kind": "Gdef"}, "STORAGE_KEY": {".class": "SymbolTableNode", "cross_ref": "gotrue.constants.STORAGE_KEY", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.Session", "kind": "Gdef"}, "SignInAnonymouslyCredentials": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.SignInAnonymouslyCredentials", "kind": "Gdef"}, "SignInWithIdTokenCredentials": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.SignInWithIdTokenCredentials", "kind": "Gdef"}, "SignInWithOAuthCredentials": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.SignInWithOAuthCredentials", "kind": "Gdef"}, "SignInWithPasswordCredentials": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.SignInWithPasswordCredentials", "kind": "Gdef"}, "SignInWithPasswordlessCredentials": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.SignInWithPasswordlessCredentials", "kind": "Gdef"}, "SignInWithSSOCredentials": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.SignInWithSSOCredentials", "kind": "Gdef"}, "SignOutOptions": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.SignOutOptions", "kind": "Gdef"}, "SignUpWithPasswordCredentials": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.SignUpWithPasswordCredentials", "kind": "Gdef"}, "Subscription": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.Subscription", "kind": "Gdef"}, "SyncClient": {".class": "SymbolTableNode", "cross_ref": "gotrue.http_clients.SyncClient", "kind": "Gdef"}, "SyncGoTrueAdminAPI": {".class": "SymbolTableNode", "cross_ref": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI", "kind": "Gdef"}, "SyncGoTrueBaseAPI": {".class": "SymbolTableNode", "cross_ref": "gotrue._sync.gotrue_base_api.SyncGoTrueBaseAPI", "kind": "Gdef"}, "SyncGoTrueClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["gotrue._sync.gotrue_base_api.SyncGoTrueBaseAPI"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient", "name": "SyncGoTrueClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "gotrue._sync.gotrue_client", "mro": ["gotrue._sync.gotrue_client.SyncGoTrueClient", "gotrue._sync.gotrue_base_api.SyncGoTrueBaseAPI", "builtins.object"], "names": {".class": "SymbolTable", "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.__del__", "name": "__del__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__del__ of SyncGoTrueClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "url", "headers", "storage_key", "auto_refresh_token", "persist_session", "storage", "http_client", "flow_type", "verify", "proxy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "url", "headers", "storage_key", "auto_refresh_token", "persist_session", "storage", "http_client", "flow_type", "verify", "proxy"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["gotrue._sync.storage.SyncSupportedStorage", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["gotrue.http_clients.SyncClient", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.AuthFlowType"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SyncGoTrueClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_auto_refresh_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._auto_refresh_token", "name": "_auto_refresh_token", "setter_type": null, "type": "builtins.bool"}}, "_call_refresh_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "refresh_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._call_refresh_token", "name": "_call_refresh_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "refresh_token"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_call_refresh_token of SyncGoTrueClient", "ret_type": "gotrue.types.Session", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_challenge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._challenge", "name": "_challenge", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.MFAChallengeParams"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_challenge of SyncGoTrueClient", "ret_type": "gotrue.types.AuthMFAChallengeResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_challenge_and_verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._challenge_and_verify", "name": "_challenge_and_verify", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.MFAChallengeAndVerifyParams"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_challenge_and_verify of SyncGoTrueClient", "ret_type": "gotrue.types.AuthMFAVerifyResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_enroll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._enroll", "name": "_enroll", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.MFAEnrollParams"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_enroll of SyncGoTrueClient", "ret_type": "gotrue.types.AuthMFAEnrollResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fetch_jwks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "kid", "jwks"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._fetch_jwks", "name": "_fetch_jwks", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "kid", "jwks"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.JWKSet"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_fetch_jwks of SyncGoTrueClient", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.JWK"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_flow_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._flow_type", "name": "_flow_type", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.AuthFlowType"}}}, "_get_authenticator_assurance_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._get_authenticator_assurance_level", "name": "_get_authenticator_assurance_level", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_authenticator_assurance_level of SyncGoTrueClient", "ret_type": "gotrue.types.AuthMFAGetAuthenticatorAssuranceLevelResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "query_params", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._get_param", "name": "_get_param", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "query_params", "name"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_param of SyncGoTrueClient", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_session_from_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._get_session_from_url", "name": "_get_session_from_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_session_from_url of SyncGoTrueClient", "ret_type": {".class": "TupleType", "implicit": false, "items": ["gotrue.types.Session", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_url_for_provider": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "url", "provider", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._get_url_for_provider", "name": "_get_url_for_provider", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "url", "provider", "params"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.Provider"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_url_for_provider of SyncGoTrueClient", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_valid_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "raw_session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._get_valid_session", "name": "_get_valid_session", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "raw_session"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_valid_session of SyncGoTrueClient", "ret_type": {".class": "UnionType", "items": ["gotrue.types.Session", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_in_memory_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._in_memory_session", "name": "_in_memory_session", "setter_type": null, "type": {".class": "UnionType", "items": ["gotrue.types.Session", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_is_implicit_grant_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._is_implicit_grant_flow", "name": "_is_implicit_grant_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_implicit_grant_flow of SyncGoTrueClient", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_jwks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._jwks", "name": "_jwks", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.JWKSet"}}}, "_jwks_cached_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._jwks_cached_at", "name": "_jwks_cached_at", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_jwks_ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._jwks_ttl", "name": "_jwks_ttl", "setter_type": null, "type": "builtins.float"}}, "_list_factors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._list_factors", "name": "_list_factors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_list_factors of SyncGoTrueClient", "ret_type": "gotrue.types.AuthMFAListFactorsResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_network_retries": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._network_retries", "name": "_network_retries", "setter_type": null, "type": "builtins.int"}}, "_notify_all_subscribers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._notify_all_subscribers", "name": "_notify_all_subscribers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "session"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.AuthChangeEvent"}, {".class": "UnionType", "items": ["gotrue.types.Session", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_notify_all_subscribers of SyncGoTrueClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_persist_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._persist_session", "name": "_persist_session", "setter_type": null, "type": "builtins.bool"}}, "_recover_and_refresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._recover_and_refresh", "name": "_recover_and_refresh", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_recover_and_refresh of SyncGoTrueClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_refresh_access_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "refresh_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._refresh_access_token", "name": "_refresh_access_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "refresh_token"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_refresh_access_token of SyncGoTrueClient", "ret_type": "gotrue.types.AuthResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_refresh_token_timer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._refresh_token_timer", "name": "_refresh_token_timer", "setter_type": null, "type": {".class": "UnionType", "items": ["gotrue.timer.Timer", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_remove_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._remove_session", "name": "_remove_session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_remove_session of SyncGoTrueClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_save_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._save_session", "name": "_save_session", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "session"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", "gotrue.types.Session"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_save_session of SyncGoTrueClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_start_auto_refresh_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._start_auto_refresh_token", "name": "_start_auto_refresh_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_start_auto_refresh_token of SyncGoTrueClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_state_change_emitters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._state_change_emitters", "name": "_state_change_emitters", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "gotrue.types.Subscription"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_storage": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._storage", "name": "_storage", "setter_type": null, "type": "gotrue._sync.storage.SyncSupportedStorage"}}, "_storage_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._storage_key", "name": "_storage_key", "setter_type": null, "type": "builtins.str"}}, "_unenroll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._unenroll", "name": "_unenroll", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.MFAUnenrollParams"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_unenroll of SyncGoTrueClient", "ret_type": "gotrue.types.AuthMFAUnenrollResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient._verify", "name": "_verify", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.MFAVerifyParams"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_verify of SyncGoTrueClient", "ret_type": "gotrue.types.AuthMFAVerifyResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "admin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.admin", "name": "admin", "setter_type": null, "type": "gotrue._sync.gotrue_admin_api.SyncGoTrueAdminAPI"}}, "exchange_code_for_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.exchange_code_for_session", "name": "exchange_code_for_session", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.CodeExchangeParams"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "exchange_code_for_session of SyncGoTrueClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_claims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "jwt", "jwks"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.get_claims", "name": "get_claims", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "jwt", "jwks"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.JWKSet"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_claims of SyncGoTrueClient", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.ClaimsResponse"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.get_session", "name": "get_session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_session of SyncGoTrueClient", "ret_type": {".class": "UnionType", "items": ["gotrue.types.Session", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "jwt"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.get_user", "name": "get_user", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "jwt"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_user of SyncGoTrueClient", "ret_type": {".class": "UnionType", "items": ["gotrue.types.UserResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_user_identities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.get_user_identities", "name": "get_user_identities", "type": null}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "url"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "initialize of SyncGoTrueClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "initialize_from_storage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.initialize_from_storage", "name": "initialize_from_storage", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "initialize_from_storage of SyncGoTrueClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "initialize_from_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.initialize_from_url", "name": "initialize_from_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "initialize_from_url of SyncGoTrueClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "link_identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.link_identity", "name": "link_identity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.SignInWithOAuthCredentials"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "link_identity of SyncGoTrueClient", "ret_type": "gotrue.types.OAuthResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mfa": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.mfa", "name": "mfa", "setter_type": null, "type": "gotrue._sync.gotrue_mfa_api.SyncGoTrueMFAAPI"}}, "on_auth_state_change": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.on_auth_state_change", "name": "on_auth_state_change", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.AuthChangeEvent"}, {".class": "UnionType", "items": ["gotrue.types.Session", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_auth_state_change of SyncGoTrueClient", "ret_type": "gotrue.types.Subscription", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reauthenticate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.reauthenticate", "name": "reauthenticate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reauthenticate of SyncGoTrueClient", "ret_type": "gotrue.types.AuthResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "refresh_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "refresh_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.refresh_session", "name": "refresh_session", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "refresh_token"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "refresh_session of SyncGoTrueClient", "ret_type": "gotrue.types.AuthResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.resend", "name": "resend", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.ResendCredentials"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "resend of SyncGoTrueClient", "ret_type": "gotrue.types.AuthOtpResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset_password_email": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "email", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.reset_password_email", "name": "reset_password_email", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "email", "options"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.Options"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reset_password_email of SyncGoTrueClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset_password_for_email": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "email", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.reset_password_for_email", "name": "reset_password_for_email", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "email", "options"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.Options"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reset_password_for_email of SyncGoTrueClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "access_token", "refresh_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.set_session", "name": "set_session", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "access_token", "refresh_token"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_session of SyncGoTrueClient", "ret_type": "gotrue.types.AuthResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign_in_anonymously": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.sign_in_anonymously", "name": "sign_in_anonymously", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "credentials"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.SignInAnonymouslyCredentials"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sign_in_anonymously of SyncGoTrueClient", "ret_type": "gotrue.types.AuthResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign_in_with_id_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.sign_in_with_id_token", "name": "sign_in_with_id_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.SignInWithIdTokenCredentials"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sign_in_with_id_token of SyncGoTrueClient", "ret_type": "gotrue.types.AuthResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign_in_with_oauth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.sign_in_with_oauth", "name": "sign_in_with_o<PERSON>h", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.SignInWithOAuthCredentials"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sign_in_with_oauth of SyncGoTrueClient", "ret_type": "gotrue.types.OAuthResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign_in_with_otp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.sign_in_with_otp", "name": "sign_in_with_otp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.SignInWithPasswordlessCredentials"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sign_in_with_otp of SyncGoTrueClient", "ret_type": "gotrue.types.AuthOtpResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign_in_with_password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.sign_in_with_password", "name": "sign_in_with_password", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.SignInWithPasswordCredentials"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sign_in_with_password of SyncGoTrueClient", "ret_type": "gotrue.types.AuthResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign_in_with_sso": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.sign_in_with_sso", "name": "sign_in_with_sso", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.SignInWithSSOCredentials"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sign_in_with_sso of SyncGoTrueClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign_out": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.sign_out", "name": "sign_out", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "options"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.SignOutOptions"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sign_out of SyncGoTrueClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign_up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.sign_up", "name": "sign_up", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.SignUpWithPasswordCredentials"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sign_up of SyncGoTrueClient", "ret_type": "gotrue.types.AuthResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unlink_identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "identity"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.unlink_identity", "name": "unlink_identity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "identity"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", "gotrue.types.UserIdentity"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "unlink_identity of SyncGoTrueClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "attributes", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.update_user", "name": "update_user", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "attributes", "options"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.UserAttributes"}, {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.UpdateUserOptions"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_user of SyncGoTrueClient", "ret_type": "gotrue.types.UserResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "verify_otp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.verify_otp", "name": "verify_otp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["gotrue._sync.gotrue_client.SyncGoTrueClient", {".class": "TypeAliasType", "args": [], "type_ref": "gotrue.types.VerifyOtpParams"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "verify_otp of SyncGoTrueClient", "ret_type": "gotrue.types.AuthResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "gotrue._sync.gotrue_client.SyncGoTrueClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "gotrue._sync.gotrue_client.SyncGoTrueClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SyncGoTrueMFAAPI": {".class": "SymbolTableNode", "cross_ref": "gotrue._sync.gotrue_mfa_api.SyncGoTrueMFAAPI", "kind": "Gdef"}, "SyncMemoryStorage": {".class": "SymbolTableNode", "cross_ref": "gotrue._sync.storage.SyncMemoryStorage", "kind": "Gdef"}, "SyncSupportedStorage": {".class": "SymbolTableNode", "cross_ref": "gotrue._sync.storage.SyncSupportedStorage", "kind": "Gdef"}, "Timer": {".class": "SymbolTableNode", "cross_ref": "gotrue.timer.Timer", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "UpdateUserOptions": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.UpdateUserOptions", "kind": "Gdef"}, "UserAttributes": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.UserAttributes", "kind": "Gdef"}, "UserIdentity": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.UserIdentity", "kind": "Gdef"}, "UserResponse": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.UserResponse", "kind": "Gdef"}, "VerifyOtpParams": {".class": "SymbolTableNode", "cross_ref": "gotrue.types.VerifyOtpParams", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gotrue._sync.gotrue_client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gotrue._sync.gotrue_client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gotrue._sync.gotrue_client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gotrue._sync.gotrue_client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gotrue._sync.gotrue_client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gotrue._sync.gotrue_client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "decode_jwt": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.decode_jwt", "kind": "Gdef"}, "generate_pkce_challenge": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.generate_pkce_challenge", "kind": "Gdef"}, "generate_pkce_verifier": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.generate_pkce_verifier", "kind": "Gdef"}, "get_algorithm_by_name": {".class": "SymbolTableNode", "cross_ref": "jwt.api_jws.get_algorithm_by_name", "kind": "Gdef"}, "loads": {".class": "SymbolTableNode", "cross_ref": "json.loads", "kind": "Gdef"}, "model_dump": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.model_dump", "kind": "Gdef"}, "model_dump_json": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.model_dump_json", "kind": "Gdef"}, "model_validate": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.model_validate", "kind": "Gdef"}, "parse_auth_otp_response": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.parse_auth_otp_response", "kind": "Gdef"}, "parse_auth_response": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.parse_auth_response", "kind": "Gdef"}, "parse_jwks": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.parse_jwks", "kind": "Gdef"}, "parse_link_identity_response": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.parse_link_identity_response", "kind": "Gdef"}, "parse_qs": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.parse_qs", "kind": "Gdef"}, "parse_sso_response": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.parse_sso_response", "kind": "Gdef"}, "parse_user_response": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.parse_user_response", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "suppress": {".class": "SymbolTableNode", "cross_ref": "contextlib.suppress", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "urlencode": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlencode", "kind": "Gdef"}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}, "uuid4": {".class": "SymbolTableNode", "cross_ref": "uuid.uuid4", "kind": "Gdef"}, "validate_exp": {".class": "SymbolTableNode", "cross_ref": "gotrue.helpers.validate_exp", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/gotrue/_sync/gotrue_client.py"}