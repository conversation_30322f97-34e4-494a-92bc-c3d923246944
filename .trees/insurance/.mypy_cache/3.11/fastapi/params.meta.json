{"data_mtime": 1755541222, "dep_lines": [5, 6, 9, 1, 2, 3, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.openapi.models", "pydantic.fields", "fastapi._compat", "warnings", "enum", "typing", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "_warnings", "abc", "annotated_types", "fastapi.openapi", "pydantic", "pydantic._internal", "pydantic._internal._repr", "pydantic.aliases", "pydantic.networks", "pydantic.types", "pydantic_core", "pydantic_core._pydantic_core", "re", "types"], "hash": "89ea0d27d0e9567c2e518b8419b603c1016aad92", "id": "fastapi.params", "ignore_all": true, "interface_hash": "9816043bdc1cf3c764071b29339f67ff24ba7d50", "mtime": 1753678816, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/fastapi/params.py", "plugin_data": null, "size": 28237, "suppressed": [], "version_id": "1.17.1"}