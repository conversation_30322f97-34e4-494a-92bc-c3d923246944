{".class": "MypyFile", "_fullname": "langchain_community.tools.gmail.utils", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Credentials": {".class": "SymbolTableNode", "cross_ref": "google.oauth2.credentials.Credentials", "kind": "Gdef"}, "DEFAULT_CLIENT_SECRETS_FILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langchain_community.tools.gmail.utils.DEFAULT_CLIENT_SECRETS_FILE", "name": "DEFAULT_CLIENT_SECRETS_FILE", "setter_type": null, "type": "builtins.str"}}, "DEFAULT_CREDS_TOKEN_FILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langchain_community.tools.gmail.utils.DEFAULT_CREDS_TOKEN_FILE", "name": "DEFAULT_CREDS_TOKEN_FILE", "setter_type": null, "type": "builtins.str"}}, "DEFAULT_SCOPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_community.tools.gmail.utils.DEFAULT_SCOPES", "name": "DEFAULT_SCOPES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "InstalledAppFlow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "langchain_community.tools.gmail.utils.InstalledAppFlow", "name": "InstalledAppFlow", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "langchain_community.tools.gmail.utils.InstalledAppFlow", "source_any": null, "type_of_any": 3}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "google.auth.transport.requests.Request", "kind": "Gdef"}, "Resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "langchain_community.tools.gmail.utils.Resource", "name": "Resource", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "langchain_community.tools.gmail.utils.Resource", "source_any": null, "type_of_any": 3}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_community.tools.gmail.utils.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_community.tools.gmail.utils.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_community.tools.gmail.utils.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_community.tools.gmail.utils.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_community.tools.gmail.utils.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_community.tools.gmail.utils.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "build_resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "langchain_community.tools.gmail.utils.build_resource", "name": "build_resource", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "langchain_community.tools.gmail.utils.build_resource", "source_any": null, "type_of_any": 3}}}, "build_resource_service": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": ["credentials", "service_name", "service_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_community.tools.gmail.utils.build_resource_service", "name": "build_resource_service", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["credentials", "service_name", "service_version"], "arg_types": [{".class": "UnionType", "items": ["google.oauth2.credentials.Credentials", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "build_resource_service", "ret_type": {".class": "AnyType", "missing_import_name": "langchain_community.tools.gmail.utils.Resource", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clean_email_body": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["body"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_community.tools.gmail.utils.clean_email_body", "name": "clean_email_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["body"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "clean_email_body", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_gmail_credentials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": ["token_file", "client_secrets_file", "scopes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_community.tools.gmail.utils.get_gmail_credentials", "name": "get_gmail_credentials", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["token_file", "client_secrets_file", "scopes"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_gmail_credentials", "ret_type": "google.oauth2.credentials.Credentials", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "guard_import": {".class": "SymbolTableNode", "cross_ref": "langchain_core.utils.utils.guard_import", "kind": "Gdef"}, "import_google": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_community.tools.gmail.utils.import_google", "name": "import_google", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "import_google", "ret_type": {".class": "TupleType", "implicit": false, "items": ["google.auth.transport.requests.Request", "google.oauth2.credentials.Credentials"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "import_googleapiclient_resource_builder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_community.tools.gmail.utils.import_googleapiclient_resource_builder", "name": "import_googleapiclient_resource_builder", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "import_googleapiclient_resource_builder", "ret_type": {".class": "AnyType", "missing_import_name": "langchain_community.tools.gmail.utils.build_resource", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "import_installed_app_flow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_community.tools.gmail.utils.import_installed_app_flow", "name": "import_installed_app_flow", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "import_installed_app_flow", "ret_type": {".class": "AnyType", "missing_import_name": "langchain_community.tools.gmail.utils.InstalledAppFlow", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_community.tools.gmail.utils.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/langchain_community/tools/gmail/utils.py"}