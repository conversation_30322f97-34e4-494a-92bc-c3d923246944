{"data_mtime": 1755541222, "dep_lines": [22, 9, 10, 19, 20, 21, 23, 3, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 221, 213, 288], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20], "dependencies": ["sqlalchemy.sql.expression", "langchain_core._api", "langchain_core.utils", "sqlalchemy.engine", "sqlalchemy.exc", "sqlalchemy.schema", "sqlalchemy.types", "__future__", "re", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "langchain_core", "langchain_core._api.deprecation", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.reflection", "sqlalchemy.engine.result", "sqlalchemy.engine.url", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types", "typing_extensions"], "hash": "56b78a49bf8669209b8aff16844a693ec0c867bf", "id": "langchain_community.utilities.sql_database", "ignore_all": true, "interface_hash": "9d9f6194f76e1ca119a530b32fa4006548820044", "mtime": 1753678823, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/langchain_community/utilities/sql_database.py", "plugin_data": null, "size": 24868, "suppressed": ["dbruntime.databricks_repl_context", "databricks", "cnosdb_connector"], "version_id": "1.17.1"}