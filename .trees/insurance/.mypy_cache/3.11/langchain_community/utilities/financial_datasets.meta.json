{"data_mtime": 1755541222, "dep_lines": [10, 6, 7, 11, 1, 1, 1, 1, 1, 1, 1, 1, 9], "dep_prios": [5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["langchain_core.utils", "json", "typing", "pydantic", "builtins", "_frozen_importlib", "abc", "langchain_core", "langchain_core.utils.env", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "9eb11cf5e8cc847b3d018f9d55772d738e5ff602", "id": "langchain_community.utilities.financial_datasets", "ignore_all": true, "interface_hash": "fe3570a1375f6e692dbe618813a37c18d265cc40", "mtime": 1753678823, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/langchain_community/utilities/financial_datasets.py", "plugin_data": null, "size": 5108, "suppressed": ["requests"], "version_id": "1.17.1"}