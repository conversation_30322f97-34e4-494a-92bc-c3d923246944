{"data_mtime": 1755541222, "dep_lines": [24, 19, 3, 4, 5, 6, 7, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 22], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["langchain_community.document_loaders.blob_loaders.schema", "urllib.parse", "contextlib", "mimetypes", "tempfile", "io", "pathlib", "typing", "builtins", "_frozen_importlib", "_io", "abc", "langchain_core", "langchain_core.document_loaders", "langchain_core.document_loaders.blob_loaders", "langchain_core.documents", "langchain_core.documents.base", "langchain_core.load", "langchain_core.load.serializable", "os", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types", "typing_extensions", "urllib"], "hash": "8767bf27addf0ff480f476f2ea66445d716d881b", "id": "langchain_community.document_loaders.blob_loaders.cloud_blob_loader", "ignore_all": true, "interface_hash": "339bd272010cb8eb6aba0d303716a6bdf6db24e3", "mtime": 1753678823, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/langchain_community/document_loaders/blob_loaders/cloud_blob_loader.py", "plugin_data": null, "size": 9731, "suppressed": ["cloudpathlib"], "version_id": "1.17.1"}