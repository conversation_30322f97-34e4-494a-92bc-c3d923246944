{".class": "MypyFile", "_fullname": "langchain._api", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "LangChainDeprecationWarning": {".class": "SymbolTableNode", "cross_ref": "langchain_core._api.deprecation.LangChainDeprecationWarning", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain._api.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain._api.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain._api.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain._api.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain._api.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain._api.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain._api.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain._api.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "create_importer": {".class": "SymbolTableNode", "cross_ref": "langchain._api.module_import.create_importer", "kind": "Gdef"}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "langchain_core._api.deprecation.deprecated", "kind": "Gdef"}, "suppress_langchain_deprecation_warning": {".class": "SymbolTableNode", "cross_ref": "langchain_core._api.deprecation.suppress_langchain_deprecation_warning", "kind": "Gdef"}, "surface_langchain_deprecation_warnings": {".class": "SymbolTableNode", "cross_ref": "langchain_core._api.deprecation.surface_langchain_deprecation_warnings", "kind": "Gdef"}, "warn_deprecated": {".class": "SymbolTableNode", "cross_ref": "langchain_core._api.deprecation.warn_deprecated", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/langchain/_api/__init__.py"}