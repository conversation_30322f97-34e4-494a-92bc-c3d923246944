{"data_mtime": 1755541222, "dep_lines": [8, 9, 10, 5, 3, 1, 1, 1], "dep_prios": [25, 5, 25, 5, 5, 5, 30, 30], "dependencies": ["langchain_core.load.dump", "langchain_core.load.load", "langchain_core.load.serializable", "langchain_core._import_utils", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "c2053008a261ca70d2985abdb1400cf2e83d3948", "id": "langchain_core.load", "ignore_all": true, "interface_hash": "2400267b994da4acdc8899340fc046b1384c48a8", "mtime": 1753678820, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/langchain_core/load/__init__.py", "plugin_data": null, "size": 1150, "suppressed": [], "version_id": "1.17.1"}