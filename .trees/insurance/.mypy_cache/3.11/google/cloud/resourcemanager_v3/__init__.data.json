{".class": "MypyFile", "_fullname": "google.cloud.resourcemanager_v3", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CreateFolderMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.CreateFolderMetadata", "kind": "Gdef"}, "CreateFolderRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.CreateFolderRequest", "kind": "Gdef"}, "CreateProjectMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.CreateProjectMetadata", "kind": "Gdef"}, "CreateProjectRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.CreateProjectRequest", "kind": "Gdef"}, "CreateTagBindingMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_bindings.CreateTagBindingMetadata", "kind": "Gdef"}, "CreateTagBindingRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_bindings.CreateTagBindingRequest", "kind": "Gdef"}, "CreateTagHoldMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_holds.CreateTagHoldMetadata", "kind": "Gdef"}, "CreateTagHoldRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_holds.CreateTagHoldRequest", "kind": "Gdef"}, "CreateTagKeyMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_keys.CreateTagKeyMetadata", "kind": "Gdef"}, "CreateTagKeyRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_keys.CreateTagKeyRequest", "kind": "Gdef"}, "CreateTagValueMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_values.CreateTagValueMetadata", "kind": "Gdef"}, "CreateTagValueRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_values.CreateTagValueRequest", "kind": "Gdef"}, "DeleteFolderMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.DeleteFolderMetadata", "kind": "Gdef"}, "DeleteFolderRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.DeleteFolderRequest", "kind": "Gdef"}, "DeleteOrganizationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.organizations.DeleteOrganizationMetadata", "kind": "Gdef"}, "DeleteProjectMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.DeleteProjectMetadata", "kind": "Gdef"}, "DeleteProjectRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.DeleteProjectRequest", "kind": "Gdef"}, "DeleteTagBindingMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_bindings.DeleteTagBindingMetadata", "kind": "Gdef"}, "DeleteTagBindingRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_bindings.DeleteTagBindingRequest", "kind": "Gdef"}, "DeleteTagHoldMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_holds.DeleteTagHoldMetadata", "kind": "Gdef"}, "DeleteTagHoldRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_holds.DeleteTagHoldRequest", "kind": "Gdef"}, "DeleteTagKeyMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_keys.DeleteTagKeyMetadata", "kind": "Gdef"}, "DeleteTagKeyRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_keys.DeleteTagKeyRequest", "kind": "Gdef"}, "DeleteTagValueMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_values.DeleteTagValueMetadata", "kind": "Gdef"}, "DeleteTagValueRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_values.DeleteTagValueRequest", "kind": "Gdef"}, "EffectiveTag": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_bindings.EffectiveTag", "kind": "Gdef"}, "Folder": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.Folder", "kind": "Gdef"}, "FoldersAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.folders.async_client.FoldersAsyncClient", "kind": "Gdef"}, "FoldersClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.folders.client.FoldersClient", "kind": "Gdef"}, "GetFolderRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.GetFolderRequest", "kind": "Gdef"}, "GetNamespacedTagKeyRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_keys.GetNamespacedTagKeyRequest", "kind": "Gdef"}, "GetNamespacedTagValueRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_values.GetNamespacedTagValueRequest", "kind": "Gdef"}, "GetOrganizationRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.organizations.GetOrganizationRequest", "kind": "Gdef"}, "GetProjectRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.GetProjectRequest", "kind": "Gdef"}, "GetTagKeyRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_keys.GetTagKeyRequest", "kind": "Gdef"}, "GetTagValueRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_values.GetTagValueRequest", "kind": "Gdef"}, "ListEffectiveTagsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_bindings.ListEffectiveTagsRequest", "kind": "Gdef"}, "ListEffectiveTagsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_bindings.ListEffectiveTagsResponse", "kind": "Gdef"}, "ListFoldersRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.ListFoldersRequest", "kind": "Gdef"}, "ListFoldersResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.ListFoldersResponse", "kind": "Gdef"}, "ListProjectsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.ListProjectsRequest", "kind": "Gdef"}, "ListProjectsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.ListProjectsResponse", "kind": "Gdef"}, "ListTagBindingsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_bindings.ListTagBindingsRequest", "kind": "Gdef"}, "ListTagBindingsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_bindings.ListTagBindingsResponse", "kind": "Gdef"}, "ListTagHoldsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_holds.ListTagHoldsRequest", "kind": "Gdef"}, "ListTagHoldsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_holds.ListTagHoldsResponse", "kind": "Gdef"}, "ListTagKeysRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_keys.ListTagKeysRequest", "kind": "Gdef"}, "ListTagKeysResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_keys.ListTagKeysResponse", "kind": "Gdef"}, "ListTagValuesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_values.ListTagValuesRequest", "kind": "Gdef"}, "ListTagValuesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_values.ListTagValuesResponse", "kind": "Gdef"}, "MoveFolderMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.MoveFolderMetadata", "kind": "Gdef"}, "MoveFolderRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.MoveFolderRequest", "kind": "Gdef"}, "MoveProjectMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.MoveProjectMetadata", "kind": "Gdef"}, "MoveProjectRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.MoveProjectRequest", "kind": "Gdef"}, "Organization": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.organizations.Organization", "kind": "Gdef"}, "OrganizationsAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.organizations.async_client.OrganizationsAsyncClient", "kind": "Gdef"}, "OrganizationsClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.organizations.client.OrganizationsClient", "kind": "Gdef"}, "Project": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.Project", "kind": "Gdef"}, "ProjectsAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.projects.async_client.ProjectsAsyncClient", "kind": "Gdef"}, "ProjectsClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.projects.client.ProjectsClient", "kind": "Gdef"}, "Purpose": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_keys.Purpose", "kind": "Gdef"}, "SearchFoldersRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.SearchFoldersRequest", "kind": "Gdef"}, "SearchFoldersResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.SearchFoldersResponse", "kind": "Gdef"}, "SearchOrganizationsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.organizations.SearchOrganizationsRequest", "kind": "Gdef"}, "SearchOrganizationsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.organizations.SearchOrganizationsResponse", "kind": "Gdef"}, "SearchProjectsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.SearchProjectsRequest", "kind": "Gdef"}, "SearchProjectsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.SearchProjectsResponse", "kind": "Gdef"}, "TagBinding": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_bindings.TagBinding", "kind": "Gdef"}, "TagBindingsAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient", "kind": "Gdef"}, "TagBindingsClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_bindings.client.TagBindingsClient", "kind": "Gdef"}, "TagHold": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_holds.TagHold", "kind": "Gdef"}, "TagHoldsAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_holds.async_client.TagHoldsAsyncClient", "kind": "Gdef"}, "TagHoldsClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_holds.client.TagHoldsClient", "kind": "Gdef"}, "TagKey": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_keys.TagKey", "kind": "Gdef"}, "TagKeysAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_keys.async_client.TagKeysAsyncClient", "kind": "Gdef"}, "TagKeysClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_keys.client.TagKeysClient", "kind": "Gdef"}, "TagValue": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_values.TagValue", "kind": "Gdef"}, "TagValuesAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_values.async_client.TagValuesAsyncClient", "kind": "Gdef"}, "TagValuesClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_values.client.TagValuesClient", "kind": "Gdef"}, "UndeleteFolderMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.UndeleteFolderMetadata", "kind": "Gdef"}, "UndeleteFolderRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.UndeleteFolderRequest", "kind": "Gdef"}, "UndeleteOrganizationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.organizations.UndeleteOrganizationMetadata", "kind": "Gdef"}, "UndeleteProjectMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.UndeleteProjectMetadata", "kind": "Gdef"}, "UndeleteProjectRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.UndeleteProjectRequest", "kind": "Gdef"}, "UpdateFolderMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.UpdateFolderMetadata", "kind": "Gdef"}, "UpdateFolderRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders.UpdateFolderRequest", "kind": "Gdef"}, "UpdateProjectMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.UpdateProjectMetadata", "kind": "Gdef"}, "UpdateProjectRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.projects.UpdateProjectRequest", "kind": "Gdef"}, "UpdateTagKeyMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_keys.UpdateTagKeyMetadata", "kind": "Gdef"}, "UpdateTagKeyRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_keys.UpdateTagKeyRequest", "kind": "Gdef"}, "UpdateTagValueMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_values.UpdateTagValueMetadata", "kind": "Gdef"}, "UpdateTagValueRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_values.UpdateTagValueRequest", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "package_version": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.gapic_version", "kind": "Gdef", "module_public": false}}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/resourcemanager_v3/__init__.py"}