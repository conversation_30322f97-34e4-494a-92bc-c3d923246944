{"data_mtime": 1755541222, "dep_lines": [21, 22, 23, 24, 25, 26, 27, 28, 46, 54, 72, 84, 93, 107, 16, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30], "dependencies": ["google.cloud.resourcemanager_v3.services.folders", "google.cloud.resourcemanager_v3.services.organizations", "google.cloud.resourcemanager_v3.services.projects", "google.cloud.resourcemanager_v3.services.tag_bindings", "google.cloud.resourcemanager_v3.services.tag_holds", "google.cloud.resourcemanager_v3.services.tag_keys", "google.cloud.resourcemanager_v3.services.tag_values", "google.cloud.resourcemanager_v3.types.folders", "google.cloud.resourcemanager_v3.types.organizations", "google.cloud.resourcemanager_v3.types.projects", "google.cloud.resourcemanager_v3.types.tag_bindings", "google.cloud.resourcemanager_v3.types.tag_holds", "google.cloud.resourcemanager_v3.types.tag_keys", "google.cloud.resourcemanager_v3.types.tag_values", "google.cloud.resourcemanager_v3.gapic_version", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "005384c96de59bcab41abb628fb57aa66763f480", "id": "google.cloud.resourcemanager_v3", "ignore_all": true, "interface_hash": "c9fc6c357a21afef53c254eb404f9dc7c1b9606a", "mtime": 1753678820, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/resourcemanager_v3/__init__.py", "plugin_data": null, "size": 5951, "suppressed": [], "version_id": "1.17.1"}