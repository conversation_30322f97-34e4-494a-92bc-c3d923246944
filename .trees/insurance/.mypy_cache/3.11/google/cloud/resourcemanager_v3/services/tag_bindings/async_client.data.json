{".class": "MypyFile", "_fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CLIENT_LOGGING_SUPPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.CLIENT_LOGGING_SUPPORTED", "name": "CLIENT_LOGGING_SUPPORTED", "setter_type": null, "type": "builtins.bool"}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ClientOptions": {".class": "SymbolTableNode", "cross_ref": "google.api_core.client_options.ClientOptions", "kind": "Gdef", "module_public": false}, "DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.base.DEFAULT_CLIENT_INFO", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef", "module_public": false}, "MutableSequence": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSequence", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "OptionalRetry": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.OptionalRetry", "line": 42, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.api_core.retry.retry_unary_async.AsyncRetry", "google.api_core.gapic_v1.method._MethodDefault", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "TagBindingsAsyncClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient", "name": "TagBindingsAsyncClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client", "mro": ["google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_ENDPOINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.DEFAULT_ENDPOINT", "name": "DEFAULT_ENDPOINT", "setter_type": null, "type": "builtins.str"}}, "DEFAULT_MTLS_ENDPOINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.DEFAULT_MTLS_ENDPOINT", "name": "DEFAULT_MTLS_ENDPOINT", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}}}, "_DEFAULT_ENDPOINT_TEMPLATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient._DEFAULT_ENDPOINT_TEMPLATE", "name": "_DEFAULT_ENDPOINT_TEMPLATE", "setter_type": null, "type": "builtins.str"}}, "_DEFAULT_UNIVERSE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient._DEFAULT_UNIVERSE", "name": "_DEFAULT_UNIVERSE", "setter_type": null, "type": "builtins.str"}}, "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aenter__ of TagBindingsAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.__aexit__", "name": "__aexit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "credentials", "transport", "client_options", "client_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "credentials", "transport", "client_options", "client_info"], "arg_types": ["google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient", {".class": "UnionType", "items": ["google.auth.credentials.Credentials", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "google.cloud.resourcemanager_v3.services.tag_bindings.transports.base.TagBindingsTransport", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.base.TagBindingsTransport", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.api_core.client_options.ClientOptions", {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.api_core.gapic_v1.client_info.ClientInfo"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TagBindingsAsyncClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient._client", "name": "_client", "setter_type": null, "type": "google.cloud.resourcemanager_v3.services.tag_bindings.client.TagBindingsClient"}}, "api_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.api_endpoint", "name": "api_endpoint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.api_endpoint", "name": "api_endpoint", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "api_endpoint of TagBindingsAsyncClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "common_billing_account_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.common_billing_account_path", "name": "common_billing_account_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["billing_account"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "common_folder_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.common_folder_path", "name": "common_folder_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["folder"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "common_location_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.common_location_path", "name": "common_location_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0, 0], "arg_names": ["project", "location"], "arg_types": ["builtins.str", "builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "common_organization_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.common_organization_path", "name": "common_organization_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["organization"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "common_project_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.common_project_path", "name": "common_project_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["project"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "create_tag_binding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "request", "tag_binding", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.create_tag_binding", "name": "create_tag_binding", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "request", "tag_binding", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient", {".class": "UnionType", "items": ["google.cloud.resourcemanager_v3.types.tag_bindings.CreateTagBindingRequest", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.cloud.resourcemanager_v3.types.tag_bindings.TagBinding", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_tag_binding of TagBindingsAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.api_core.operation_async.AsyncOperation"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_tag_binding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "request", "name", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.delete_tag_binding", "name": "delete_tag_binding", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "request", "name", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient", {".class": "UnionType", "items": ["google.cloud.resourcemanager_v3.types.tag_bindings.DeleteTagBindingRequest", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_tag_binding of TagBindingsAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.api_core.operation_async.AsyncOperation"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_service_account_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "filename", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.from_service_account_file", "name": "from_service_account_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "filename", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_service_account_file of TagBindingsAsyncClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.from_service_account_file", "name": "from_service_account_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "filename", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_service_account_file of TagBindingsAsyncClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_service_account_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "info", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.from_service_account_info", "name": "from_service_account_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "info", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_service_account_info of TagBindingsAsyncClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.from_service_account_info", "name": "from_service_account_info", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "info", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_service_account_info of TagBindingsAsyncClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_service_account_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.from_service_account_json", "name": "from_service_account_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "filename", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_mtls_endpoint_and_cert_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["cls", "client_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.get_mtls_endpoint_and_cert_source", "name": "get_mtls_endpoint_and_cert_source", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "client_options"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient"}, {".class": "UnionType", "items": ["google.api_core.client_options.ClientOptions", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_mtls_endpoint_and_cert_source of TagBindingsAsyncClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.get_mtls_endpoint_and_cert_source", "name": "get_mtls_endpoint_and_cert_source", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "client_options"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient"}, {".class": "UnionType", "items": ["google.api_core.client_options.ClientOptions", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_mtls_endpoint_and_cert_source of TagBindingsAsyncClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.get_operation", "name": "get_operation", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_operation of TagBindingsAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_transport_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.get_transport_class", "name": "get_transport_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["label"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.base.TagBindingsTransport"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_effective_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "request", "parent", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.list_effective_tags", "name": "list_effective_tags", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "request", "parent", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient", {".class": "UnionType", "items": ["google.cloud.resourcemanager_v3.types.tag_bindings.ListEffectiveTagsRequest", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_effective_tags of TagBindingsAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.resourcemanager_v3.services.tag_bindings.pagers.ListEffectiveTagsAsyncPager"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_tag_bindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "request", "parent", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.list_tag_bindings", "name": "list_tag_bindings", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "request", "parent", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient", {".class": "UnionType", "items": ["google.cloud.resourcemanager_v3.types.tag_bindings.ListTagBindingsRequest", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_tag_bindings of TagBindingsAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.resourcemanager_v3.services.tag_bindings.pagers.ListTagBindingsAsyncPager"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_common_billing_account_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.parse_common_billing_account_path", "name": "parse_common_billing_account_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "parse_common_folder_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.parse_common_folder_path", "name": "parse_common_folder_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "parse_common_location_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.parse_common_location_path", "name": "parse_common_location_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "parse_common_organization_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.parse_common_organization_path", "name": "parse_common_organization_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "parse_common_project_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.parse_common_project_path", "name": "parse_common_project_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "parse_tag_binding_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.parse_tag_binding_path", "name": "parse_tag_binding_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "parse_tag_key_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.parse_tag_key_path", "name": "parse_tag_key_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "parse_tag_value_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.parse_tag_value_path", "name": "parse_tag_value_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "tag_binding_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.tag_binding_path", "name": "tag_binding_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["tag_binding"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "tag_key_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.tag_key_path", "name": "tag_key_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["tag_key"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "tag_value_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.tag_value_path", "name": "tag_value_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["tag_value"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "transport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.transport", "name": "transport", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transport of TagBindingsAsyncClient", "ret_type": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.base.TagBindingsTransport", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.transport", "name": "transport", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transport of TagBindingsAsyncClient", "ret_type": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.base.TagBindingsTransport", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "universe_domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.universe_domain", "name": "universe_domain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "universe_domain of TagBindingsAsyncClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.universe_domain", "name": "universe_domain", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "universe_domain of TagBindingsAsyncClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.TagBindingsAsyncClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TagBindingsClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_bindings.client.TagBindingsClient", "kind": "Gdef", "module_public": false}, "TagBindingsGrpcAsyncIOTransport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.grpc_asyncio.TagBindingsGrpcAsyncIOTransport", "kind": "Gdef", "module_public": false}, "TagBindingsTransport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.base.TagBindingsTransport", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client._LOGGER", "name": "_LOGGER", "setter_type": null, "type": "logging.Logger"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "client_logging": {".class": "SymbolTableNode", "cross_ref": "google.api_core.client_logging", "kind": "Gdef", "module_public": false}, "core_exceptions": {".class": "SymbolTableNode", "cross_ref": "google.api_core.exceptions", "kind": "Gdef", "module_public": false}, "empty_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.empty_pb2", "name": "empty_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.empty_pb2", "source_any": null, "type_of_any": 3}}}, "ga_credentials": {".class": "SymbolTableNode", "cross_ref": "google.auth.credentials", "kind": "Gdef", "module_public": false}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef", "module_public": false}, "operation": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operation", "kind": "Gdef", "module_public": false}, "operation_async": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operation_async", "kind": "Gdef", "module_public": false}, "operations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.operations_pb2", "name": "operations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.resourcemanager_v3.services.tag_bindings.async_client.operations_pb2", "source_any": null, "type_of_any": 3}}}, "package_version": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.gapic_version", "kind": "Gdef", "module_public": false}, "pagers": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_bindings.pagers", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "retries": {".class": "SymbolTableNode", "cross_ref": "google.api_core.retry_async", "kind": "Gdef", "module_public": false}, "service_account": {".class": "SymbolTableNode", "cross_ref": "google.oauth2.service_account", "kind": "Gdef", "module_public": false}, "std_logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "tag_bindings": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_bindings", "kind": "Gdef", "module_public": false}}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/resourcemanager_v3/services/tag_bindings/async_client.py"}