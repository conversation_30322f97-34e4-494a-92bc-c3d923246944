{".class": "MypyFile", "_fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.folders.transports.base.DEFAULT_CLIENT_INFO", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "FoldersTransport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.folders.transports.base.FoldersTransport", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_BaseFoldersRestTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.resourcemanager_v3.services.folders.transports.base.FoldersTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport", "name": "_BaseFoldersRestTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport", "google.cloud.resourcemanager_v3.services.folders.transports.base.FoldersTransport", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_BaseCreateFolder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder", "name": "_BaseCreateFolder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseCreateFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseCreateFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseCreateFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseCreateFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseCreateFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseCreateFolder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseDeleteFolder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder", "name": "_BaseDeleteFolder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseDeleteFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseDeleteFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseDeleteFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseDeleteFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseDeleteFolder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseGetFolder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder", "name": "_BaseGetFolder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseGetFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseGetFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseGetFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseGetFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetFolder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseGetIamPolicy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy", "name": "_BaseGetIamPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseGetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseGetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseGetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseGetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseGetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetIamPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseGetOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetOperation", "name": "_BaseGetOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetOperation", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetOperation.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetOperation._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetOperation._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseGetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetOperation._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetOperation._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseGetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetOperation._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetOperation._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseGetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseGetOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseListFolders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders", "name": "_BaseListFolders", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseListFolders", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseListFolders", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseListFolders", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseListFolders", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseListFolders", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseMoveFolder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder", "name": "_BaseMoveFolder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseMoveFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseMoveFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseMoveFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseMoveFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseMoveFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseMoveFolder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseSearchFolders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSearchFolders", "name": "_BaseSearchFolders", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSearchFolders", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSearchFolders", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSearchFolders.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSearchFolders._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSearchFolders._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseSearchFolders", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSearchFolders._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSearchFolders._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseSearchFolders", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSearchFolders._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSearchFolders._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseSearchFolders", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSearchFolders.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSearchFolders", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseSetIamPolicy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy", "name": "_BaseSetIamPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseSetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseSetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseSetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseSetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseSetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseSetIamPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseTestIamPermissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions", "name": "_BaseTestIamPermissions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseTestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseTestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseTestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseTestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseTestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseTestIamPermissions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseUndeleteFolder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder", "name": "_BaseUndeleteFolder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseUndeleteFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseUndeleteFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseUndeleteFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseUndeleteFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseUndeleteFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUndeleteFolder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseUpdateFolder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder", "name": "_BaseUpdateFolder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseUpdateFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseUpdateFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseUpdateFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseUpdateFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseUpdateFolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport._BaseUpdateFolder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "always_use_jwt_access", "url_scheme", "api_audience"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "always_use_jwt_access", "url_scheme", "api_audience"], "arg_types": ["google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport", "builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.api_core.gapic_v1.client_info.ClientInfo", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _BaseFoldersRestTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base._BaseFoldersRestTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "folders": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.folders", "kind": "Gdef", "module_public": false}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef", "module_public": false}, "iam_policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.iam_policy_pb2", "name": "iam_policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.iam_policy_pb2", "source_any": null, "type_of_any": 3}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "json_format": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.json_format", "name": "json_format", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.json_format", "source_any": null, "type_of_any": 3}}}, "operations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.operations_pb2", "name": "operations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.operations_pb2", "source_any": null, "type_of_any": 3}}}, "path_template": {".class": "SymbolTableNode", "cross_ref": "google.api_core.path_template", "kind": "Gdef", "module_public": false}, "policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.policy_pb2", "name": "policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.resourcemanager_v3.services.folders.transports.rest_base.policy_pb2", "source_any": null, "type_of_any": 3}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/resourcemanager_v3/services/folders/transports/rest_base.py"}