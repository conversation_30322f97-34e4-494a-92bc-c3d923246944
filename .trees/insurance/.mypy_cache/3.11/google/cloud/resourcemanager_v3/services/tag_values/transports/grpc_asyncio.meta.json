{"data_mtime": 1755541222, "dep_lines": [39, 40, 37, 27, 37, 23, 24, 24, 24, 25, 26, 43, 23, 26, 16, 17, 18, 19, 20, 21, 32, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 28, 31, 32, 30, 32, 34, 33, 35], "dep_prios": [5, 5, 10, 5, 20, 10, 10, 10, 10, 10, 10, 10, 20, 20, 10, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10, 5, 20, 5, 10, 10], "dependencies": ["google.cloud.resourcemanager_v3.services.tag_values.transports.base", "google.cloud.resourcemanager_v3.services.tag_values.transports.grpc", "google.cloud.resourcemanager_v3.types.tag_values", "google.auth.transport.grpc", "google.cloud.resourcemanager_v3.types", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.grpc_helpers_async", "google.api_core.operations_v1", "google.api_core.retry_async", "google.auth.credentials", "google.api_core.client_logging", "google.api_core", "google.auth", "inspect", "json", "logging", "pickle", "typing", "warnings", "google", "builtins", "_frozen_importlib", "_warnings", "abc", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method_async", "google.api_core.operations_v1.operations_async_client", "google.api_core.retry", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary_async", "google.auth._credentials_base", "google.auth.transport", "types"], "hash": "5f7ce334115a4aa6cd3cec9c35fec63806ff9204", "id": "google.cloud.resourcemanager_v3.services.tag_values.transports.grpc_asyncio", "ignore_all": true, "interface_hash": "b3dc792f785c127654b3a24432c15f40656cf67e", "mtime": 1753678820, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/resourcemanager_v3/services/tag_values/transports/grpc_asyncio.py", "plugin_data": null, "size": 32096, "suppressed": ["google.iam.v1", "google.protobuf.json_format", "google.protobuf.message", "google.longrunning", "google.protobuf", "grpc.experimental", "grpc", "proto"], "version_id": "1.17.1"}