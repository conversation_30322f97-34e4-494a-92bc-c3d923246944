{"data_mtime": 1755541222, "dep_lines": [31, 30, 31, 20, 21, 21, 22, 24, 28, 30, 19, 23, 28, 16, 17, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25, 27], "dep_prios": [10, 10, 20, 10, 10, 10, 10, 10, 10, 20, 10, 10, 20, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["google.cloud.resourcemanager_v3.types.projects", "google.cloud.resourcemanager_v3.gapic_version", "google.cloud.resourcemanager_v3.types", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.operations_v1", "google.api_core.retry", "google.auth.credentials", "google.oauth2.service_account", "google.cloud.resourcemanager_v3", "google.api_core", "google.auth", "google.oauth2", "abc", "typing", "google", "builtins", "_frozen_importlib", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.auth._credentials_base", "google.auth._default", "types"], "hash": "ece5986074e1055deb7d183d83c47c76f1c48c2d", "id": "google.cloud.resourcemanager_v3.services.projects.transports.base", "ignore_all": true, "interface_hash": "20b3f82b2b66ed0a880d34c601dea74f90128598", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/resourcemanager_v3/services/projects/transports/base.py", "plugin_data": null, "size": 12507, "suppressed": ["google.iam.v1", "google.longrunning"], "version_id": "1.17.1"}