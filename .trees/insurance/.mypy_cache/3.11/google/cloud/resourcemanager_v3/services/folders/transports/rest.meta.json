{"data_mtime": 1755541222, "dep_lines": [35, 36, 33, 26, 33, 22, 22, 22, 22, 23, 24, 25, 44, 22, 25, 16, 17, 18, 19, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 27, 29, 30, 31], "dep_prios": [5, 5, 10, 5, 20, 10, 10, 10, 10, 10, 10, 10, 10, 20, 20, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5], "dependencies": ["google.cloud.resourcemanager_v3.services.folders.transports.base", "google.cloud.resourcemanager_v3.services.folders.transports.rest_base", "google.cloud.resourcemanager_v3.types.folders", "google.auth.transport.requests", "google.cloud.resourcemanager_v3.types", "google.api_core.gapic_v1", "google.api_core.operations_v1", "google.api_core.rest_helpers", "google.api_core.rest_streaming", "google.api_core.exceptions", "google.api_core.retry", "google.auth.credentials", "google.api_core.client_logging", "google.api_core", "google.auth", "dataclasses", "json", "logging", "typing", "warnings", "builtins", "_frozen_importlib", "abc", "enum", "google.api_core.client_info", "google.api_core.client_options", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.operations_v1.abstract_operations_base_client", "google.api_core.operations_v1.abstract_operations_client", "google.api_core.operations_v1.transports", "google.api_core.operations_v1.transports.base", "google.api_core.operations_v1.transports.rest", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.auth._credentials_base", "google.auth.transport"], "hash": "57abc48f14ddcbaed21d8922f6a17ccc674c0866", "id": "google.cloud.resourcemanager_v3.services.folders.transports.rest", "ignore_all": true, "interface_hash": "56bc5e96fc1020b37473b935b268bee064cd2134", "mtime": 1753678820, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/resourcemanager_v3/services/folders/transports/rest.py", "plugin_data": null, "size": 118492, "suppressed": ["google.iam.v1", "google.longrunning", "google.protobuf", "requests"], "version_id": "1.17.1"}