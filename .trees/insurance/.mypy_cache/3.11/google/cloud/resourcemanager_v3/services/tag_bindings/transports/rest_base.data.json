{".class": "MypyFile", "_fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.base.DEFAULT_CLIENT_INFO", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "TagBindingsTransport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.base.TagBindingsTransport", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_BaseTagBindingsRestTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.resourcemanager_v3.services.tag_bindings.transports.base.TagBindingsTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport", "name": "_BaseTagBindingsRestTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport", "google.cloud.resourcemanager_v3.services.tag_bindings.transports.base.TagBindingsTransport", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_BaseCreateTagBinding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding", "name": "_BaseCreateTagBinding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseCreateTagBinding", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseCreateTagBinding", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseCreateTagBinding", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseCreateTagBinding", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseCreateTagBinding", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseCreateTagBinding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseDeleteTagBinding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding", "name": "_BaseDeleteTagBinding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseDeleteTagBinding", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseDeleteTagBinding", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseDeleteTagBinding", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseDeleteTagBinding", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseDeleteTagBinding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseGetOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseGetOperation", "name": "_BaseGetOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseGetOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseGetOperation", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseGetOperation.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseGetOperation._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseGetOperation._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseGetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseGetOperation._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseGetOperation._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseGetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseGetOperation._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseGetOperation._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseGetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseGetOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseGetOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseListEffectiveTags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags", "name": "_BaseListEffectiveTags", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseListEffectiveTags", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseListEffectiveTags", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseListEffectiveTags", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseListEffectiveTags", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListEffectiveTags", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseListTagBindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings", "name": "_BaseListTagBindings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base", "mro": ["google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseListTagBindings", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseListTagBindings", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseListTagBindings", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseListTagBindings", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport._BaseListTagBindings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "always_use_jwt_access", "url_scheme", "api_audience"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "always_use_jwt_access", "url_scheme", "api_audience"], "arg_types": ["google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport", "builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.api_core.gapic_v1.client_info.ClientInfo", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _BaseTagBindingsRestTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base._BaseTagBindingsRestTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "json_format": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base.json_format", "name": "json_format", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base.json_format", "source_any": null, "type_of_any": 3}}}, "operations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base.operations_pb2", "name": "operations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.resourcemanager_v3.services.tag_bindings.transports.rest_base.operations_pb2", "source_any": null, "type_of_any": 3}}}, "path_template": {".class": "SymbolTableNode", "cross_ref": "google.api_core.path_template", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "tag_bindings": {".class": "SymbolTableNode", "cross_ref": "google.cloud.resourcemanager_v3.types.tag_bindings", "kind": "Gdef", "module_public": false}}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/resourcemanager_v3/services/tag_bindings/transports/rest_base.py"}