{"data_mtime": 1755541222, "dep_lines": [58, 59, 54, 57, 54, 55, 39, 55, 32, 33, 34, 35, 36, 37, 39, 46, 47, 62, 32, 36, 37, 16, 17, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 48, 50, 51], "dep_prios": [5, 5, 10, 5, 20, 10, 10, 20, 10, 10, 10, 5, 10, 10, 20, 10, 10, 10, 20, 20, 20, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["google.cloud.resourcemanager_v3.services.tag_keys.transports.base", "google.cloud.resourcemanager_v3.services.tag_keys.transports.grpc_asyncio", "google.cloud.resourcemanager_v3.services.tag_keys.pagers", "google.cloud.resourcemanager_v3.services.tag_keys.client", "google.cloud.resourcemanager_v3.services.tag_keys", "google.cloud.resourcemanager_v3.types.tag_keys", "google.cloud.resourcemanager_v3.gapic_version", "google.cloud.resourcemanager_v3.types", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.retry_async", "google.api_core.client_options", "google.auth.credentials", "google.oauth2.service_account", "google.cloud.resourcemanager_v3", "google.api_core.operation", "google.api_core.operation_async", "google.api_core.client_logging", "google.api_core", "google.auth", "google.oauth2", "collections", "logging", "re", "typing", "builtins", "_frozen_importlib", "abc", "enum", "google.api_core.client_info", "google.api_core.future", "google.api_core.future.async_future", "google.api_core.future.base", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.gapic_v1.routing_header", "google.api_core.retry", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary_async", "google.auth._credentials_base", "google.cloud.resourcemanager_v3.services.tag_keys.transports", "types"], "hash": "b2855e7709975333ea718f900ec0eb41a16b160a", "id": "google.cloud.resourcemanager_v3.services.tag_keys.async_client", "ignore_all": true, "interface_hash": "fabe6df35b55b791d0f05b236f43fa3a44f21407", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/resourcemanager_v3/services/tag_keys/async_client.py", "plugin_data": null, "size": 66895, "suppressed": ["google.iam.v1", "google.longrunning", "google.protobuf"], "version_id": "1.17.1"}