{"data_mtime": 1755541222, "dep_lines": [28, 26, 26, 20, 20, 20, 16, 17, 18, 1, 1, 1, 1, 1, 21, 23, 24], "dep_prios": [5, 10, 20, 10, 10, 20, 10, 10, 5, 5, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["google.cloud.resourcemanager_v3.services.tag_values.transports.base", "google.cloud.resourcemanager_v3.types.tag_values", "google.cloud.resourcemanager_v3.types", "google.api_core.gapic_v1", "google.api_core.path_template", "google.api_core", "json", "re", "typing", "builtins", "_frozen_importlib", "abc", "google.api_core.client_info", "google.api_core.gapic_v1.client_info"], "hash": "a1653219c1862cdb7da2baa325eca1db5a5e55d2", "id": "google.cloud.resourcemanager_v3.services.tag_values.transports.rest_base", "ignore_all": true, "interface_hash": "8830b99476c0e5b08eae677d8d68f0da4b45d454", "mtime": 1753678820, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/resourcemanager_v3/services/tag_values/transports/rest_base.py", "plugin_data": null, "size": 20668, "suppressed": ["google.iam.v1", "google.longrunning", "google.protobuf"], "version_id": "1.17.1"}