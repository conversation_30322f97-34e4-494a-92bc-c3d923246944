{"data_mtime": 1755541222, "dep_lines": [71, 72, 73, 74, 68, 68, 69, 43, 44, 47, 69, 37, 38, 39, 40, 41, 42, 43, 45, 47, 55, 656, 37, 41, 45, 16, 17, 18, 19, 20, 21, 22, 35, 656, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 63, 65, 66], "dep_prios": [5, 5, 5, 5, 10, 20, 10, 10, 5, 10, 20, 10, 10, 10, 10, 10, 5, 20, 10, 20, 10, 20, 20, 20, 20, 5, 5, 10, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["google.cloud.resourcemanager_v3.services.organizations.transports.base", "google.cloud.resourcemanager_v3.services.organizations.transports.grpc", "google.cloud.resourcemanager_v3.services.organizations.transports.grpc_asyncio", "google.cloud.resourcemanager_v3.services.organizations.transports.rest", "google.cloud.resourcemanager_v3.services.organizations.pagers", "google.cloud.resourcemanager_v3.services.organizations", "google.cloud.resourcemanager_v3.types.organizations", "google.auth.transport.mtls", "google.auth.transport.grpc", "google.cloud.resourcemanager_v3.gapic_version", "google.cloud.resourcemanager_v3.types", "google.api_core.client_options", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.retry", "google.auth.credentials", "google.auth.exceptions", "google.auth.transport", "google.oauth2.service_account", "google.cloud.resourcemanager_v3", "google.api_core.client_logging", "google.auth._default", "google.api_core", "google.auth", "google.oauth2", "collections", "http", "json", "logging", "os", "re", "typing", "warnings", "google", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.gapic_v1.routing_header", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.auth._credentials_base", "google.cloud.resourcemanager_v3.services.organizations.transports", "google.cloud.resourcemanager_v3.services.organizations.transports.rest_base", "types", "typing_extensions"], "hash": "a00079bb1e0a1db60be2cbc7c2a696aeddc9a66d", "id": "google.cloud.resourcemanager_v3.services.organizations.client", "ignore_all": true, "interface_hash": "a0295e9df77ea077bc4cbddfccba774a268313c6", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/resourcemanager_v3/services/organizations/client.py", "plugin_data": null, "size": 63842, "suppressed": ["google.iam.v1", "google.longrunning", "google.protobuf"], "version_id": "1.17.1"}