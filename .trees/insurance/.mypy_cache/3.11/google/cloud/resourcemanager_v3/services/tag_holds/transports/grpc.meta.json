{"data_mtime": 1755541222, "dep_lines": [34, 32, 25, 32, 22, 22, 22, 24, 37, 22, 23, 16, 17, 18, 19, 20, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 27, 28, 26, 28, 29, 30], "dep_prios": [5, 10, 5, 20, 10, 10, 10, 10, 10, 20, 10, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10, 5, 20, 10, 10], "dependencies": ["google.cloud.resourcemanager_v3.services.tag_holds.transports.base", "google.cloud.resourcemanager_v3.types.tag_holds", "google.auth.transport.grpc", "google.cloud.resourcemanager_v3.types", "google.api_core.gapic_v1", "google.api_core.grpc_helpers", "google.api_core.operations_v1", "google.auth.credentials", "google.api_core.client_logging", "google.api_core", "google.auth", "json", "logging", "pickle", "typing", "warnings", "google", "builtins", "_frozen_importlib", "_warnings", "abc", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.operations_v1.operations_client", "google.auth._credentials_base", "google.auth.transport", "types"], "hash": "2c4bfa6cfdd7475b347102ff6f339173df499592", "id": "google.cloud.resourcemanager_v3.services.tag_holds.transports.grpc", "ignore_all": true, "interface_hash": "efd1ed7f7038015a4eaad96d096b6b466e9a40ab", "mtime": 1753678820, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/resourcemanager_v3/services/tag_holds/transports/grpc.py", "plugin_data": null, "size": 20020, "suppressed": ["google.protobuf.json_format", "google.protobuf.message", "google.longrunning", "google.protobuf", "grpc", "proto"], "version_id": "1.17.1"}