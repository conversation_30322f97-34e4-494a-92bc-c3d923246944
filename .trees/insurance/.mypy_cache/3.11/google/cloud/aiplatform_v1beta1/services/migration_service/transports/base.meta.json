{"data_mtime": 1755541228, "dep_lines": [30, 19, 30, 19, 23, 24, 25, 26, 27, 28, 21, 22, 28, 16, 17, 21, 1, 1, 1, 1, 1, 1, 1, 1, 31, 32, 34], "dep_prios": [10, 10, 20, 20, 10, 10, 10, 10, 10, 10, 10, 10, 20, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["google.cloud.aiplatform_v1beta1.types.migration_service", "google.cloud.aiplatform_v1beta1.gapic_version", "google.cloud.aiplatform_v1beta1.types", "google.cloud.aiplatform_v1beta1", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.retry", "google.api_core.operations_v1", "google.auth.credentials", "google.oauth2.service_account", "google.auth", "google.api_core", "google.oauth2", "abc", "typing", "google", "builtins", "_frozen_importlib", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.auth._credentials_base", "google.auth._default", "types"], "hash": "c30d88af7aa5608a4426cab072ccc86f3f942a63", "id": "google.cloud.aiplatform_v1beta1.services.migration_service.transports.base", "ignore_all": true, "interface_hash": "4534f9255b1181e4c5e0bdd733f98b3d8d823915", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/migration_service/transports/base.py", "plugin_data": null, "size": 11667, "suppressed": ["google.cloud.location", "google.iam.v1", "google.longrunning"], "version_id": "1.17.1"}