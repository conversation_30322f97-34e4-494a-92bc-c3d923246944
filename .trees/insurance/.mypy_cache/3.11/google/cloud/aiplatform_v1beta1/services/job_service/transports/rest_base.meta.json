{"data_mtime": 1755541228, "dep_lines": [24, 30, 34, 36, 40, 44, 45, 49, 30, 17, 18, 17, 16, 26, 27, 1, 1, 1, 1, 1, 21, 23, 20, 52], "dep_prios": [5, 10, 10, 10, 10, 10, 10, 10, 20, 10, 10, 20, 10, 10, 5, 5, 30, 30, 30, 30, 5, 5, 5, 5], "dependencies": ["google.cloud.aiplatform_v1beta1.services.job_service.transports.base", "google.cloud.aiplatform_v1beta1.types.batch_prediction_job", "google.cloud.aiplatform_v1beta1.types.custom_job", "google.cloud.aiplatform_v1beta1.types.data_labeling_job", "google.cloud.aiplatform_v1beta1.types.hyperparameter_tuning_job", "google.cloud.aiplatform_v1beta1.types.job_service", "google.cloud.aiplatform_v1beta1.types.model_deployment_monitoring_job", "google.cloud.aiplatform_v1beta1.types.nas_job", "google.cloud.aiplatform_v1beta1.types", "google.api_core.path_template", "google.api_core.gapic_v1", "google.api_core", "json", "re", "typing", "builtins", "_frozen_importlib", "abc", "google.api_core.client_info", "google.api_core.gapic_v1.client_info"], "hash": "65f85153291bd1fb9e899c51e95eb665eff0837e", "id": "google.cloud.aiplatform_v1beta1.services.job_service.transports.rest_base", "ignore_all": true, "interface_hash": "88a656fbec9aab1f4b2169abea1003f8d0085b29", "mtime": 1753678821, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/job_service/transports/rest_base.py", "plugin_data": null, "size": 175355, "suppressed": ["google.iam.v1", "google.cloud.location", "google.protobuf", "google.longrunning"], "version_id": "1.17.1"}