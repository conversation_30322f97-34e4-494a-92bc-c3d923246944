{"data_mtime": 1755541229, "dep_lines": [56, 57, 58, 49, 34, 49, 34, 36, 37, 38, 39, 40, 41, 37, 40, 41, 16, 17, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 50, 51, 53, 54, 55], "dep_prios": [5, 5, 5, 10, 10, 20, 20, 5, 10, 10, 10, 10, 10, 20, 20, 20, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5], "dependencies": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.base", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.grpc_asyncio", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.client", "google.cloud.aiplatform_v1beta1.types.feature_online_store_service", "google.cloud.aiplatform_v1beta1.gapic_version", "google.cloud.aiplatform_v1beta1.types", "google.cloud.aiplatform_v1beta1", "google.api_core.client_options", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.retry_async", "google.auth.credentials", "google.oauth2.service_account", "google.api_core", "google.auth", "google.oauth2", "collections", "re", "typing", "builtins", "_frozen_importlib", "abc", "enum", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.gapic_v1.routing_header", "google.api_core.retry", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary_async", "google.auth._credentials_base", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports", "types"], "hash": "9f2cbc5abbc9e9bd2d289098e1133b497574017c", "id": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.async_client", "ignore_all": true, "interface_hash": "169bbbb94e570869ee7609bf887771e06892f1a1", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/feature_online_store_service/async_client.py", "plugin_data": null, "size": 53262, "suppressed": ["google.cloud.location", "google.iam.v1", "google.longrunning", "google.protobuf", "google.rpc"], "version_id": "1.17.1"}