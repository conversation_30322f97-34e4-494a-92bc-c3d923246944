{".class": "MypyFile", "_fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ClientOptions": {".class": "SymbolTableNode", "cross_ref": "google.api_core.client_options.ClientOptions", "kind": "Gdef", "module_public": false}, "DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.transports.base.DEFAULT_CLIENT_INFO", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "IndexEndpointServiceAsyncClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", "name": "IndexEndpointServiceAsyncClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client", "mro": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_ENDPOINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.DEFAULT_ENDPOINT", "name": "DEFAULT_ENDPOINT", "setter_type": null, "type": "builtins.str"}}, "DEFAULT_MTLS_ENDPOINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.DEFAULT_MTLS_ENDPOINT", "name": "DEFAULT_MTLS_ENDPOINT", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}}}, "_DEFAULT_ENDPOINT_TEMPLATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient._DEFAULT_ENDPOINT_TEMPLATE", "name": "_DEFAULT_ENDPOINT_TEMPLATE", "setter_type": null, "type": "builtins.str"}}, "_DEFAULT_UNIVERSE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient._DEFAULT_UNIVERSE", "name": "_DEFAULT_UNIVERSE", "setter_type": null, "type": "builtins.str"}}, "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aenter__ of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.__aexit__", "name": "__aexit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "credentials", "transport", "client_options", "client_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "credentials", "transport", "client_options", "client_info"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": ["google.auth.credentials.Credentials", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.transports.base.IndexEndpointServiceTransport", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.transports.base.IndexEndpointServiceTransport", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.api_core.client_options.ClientOptions", {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.api_core.gapic_v1.client_info.ClientInfo"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of IndexEndpointServiceAsyncClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient._client", "name": "_client", "setter_type": null, "type": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.client.IndexEndpointServiceClient"}}, "api_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.api_endpoint", "name": "api_endpoint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.api_endpoint", "name": "api_endpoint", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "api_endpoint of IndexEndpointServiceAsyncClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.cancel_operation", "name": "cancel_operation", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cancel_operation of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "common_billing_account_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.common_billing_account_path", "name": "common_billing_account_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["billing_account"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "common_folder_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.common_folder_path", "name": "common_folder_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["folder"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "common_location_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.common_location_path", "name": "common_location_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0, 0], "arg_names": ["project", "location"], "arg_types": ["builtins.str", "builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "common_organization_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.common_organization_path", "name": "common_organization_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["organization"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "common_project_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.common_project_path", "name": "common_project_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["project"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "create_index_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "request", "parent", "index_endpoint", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.create_index_endpoint", "name": "create_index_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "request", "parent", "index_endpoint", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.types.index_endpoint_service.CreateIndexEndpointRequest", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.types.index_endpoint.IndexEndpoint", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_index_endpoint of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.api_core.operation_async.AsyncOperation"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_index_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "request", "name", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.delete_index_endpoint", "name": "delete_index_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "request", "name", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.types.index_endpoint_service.DeleteIndexEndpointRequest", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_index_endpoint of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.api_core.operation_async.AsyncOperation"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.delete_operation", "name": "delete_operation", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_operation of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deploy_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "request", "index_endpoint", "deployed_index", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.deploy_index", "name": "deploy_index", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "request", "index_endpoint", "deployed_index", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.types.index_endpoint_service.DeployIndexRequest", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.types.index_endpoint.DeployedIndex", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "deploy_index of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.api_core.operation_async.AsyncOperation"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_service_account_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "filename", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.from_service_account_file", "name": "from_service_account_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "filename", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_service_account_file of IndexEndpointServiceAsyncClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.from_service_account_file", "name": "from_service_account_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "filename", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_service_account_file of IndexEndpointServiceAsyncClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_service_account_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "info", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.from_service_account_info", "name": "from_service_account_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "info", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_service_account_info of IndexEndpointServiceAsyncClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.from_service_account_info", "name": "from_service_account_info", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "info", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_service_account_info of IndexEndpointServiceAsyncClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_service_account_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.from_service_account_json", "name": "from_service_account_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "filename", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.get_iam_policy", "name": "get_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_iam_policy of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_index_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "request", "name", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.get_index_endpoint", "name": "get_index_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "request", "name", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.types.index_endpoint_service.GetIndexEndpointRequest", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_index_endpoint of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.index_endpoint.IndexEndpoint"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.get_location", "name": "get_location", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_location of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_mtls_endpoint_and_cert_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["cls", "client_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.get_mtls_endpoint_and_cert_source", "name": "get_mtls_endpoint_and_cert_source", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "client_options"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient"}, {".class": "UnionType", "items": ["google.api_core.client_options.ClientOptions", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_mtls_endpoint_and_cert_source of IndexEndpointServiceAsyncClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.get_mtls_endpoint_and_cert_source", "name": "get_mtls_endpoint_and_cert_source", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "client_options"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient"}, {".class": "UnionType", "items": ["google.api_core.client_options.ClientOptions", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_mtls_endpoint_and_cert_source of IndexEndpointServiceAsyncClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.get_operation", "name": "get_operation", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_operation of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_transport_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.get_transport_class", "name": "get_transport_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["label"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.transports.base.IndexEndpointServiceTransport"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "index_endpoint_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.index_endpoint_path", "name": "index_endpoint_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0, 0, 0], "arg_names": ["project", "location", "index_endpoint"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "index_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.index_path", "name": "index_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0, 0, 0], "arg_names": ["project", "location", "index"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "list_index_endpoints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "request", "parent", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.list_index_endpoints", "name": "list_index_endpoints", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "request", "parent", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.types.index_endpoint_service.ListIndexEndpointsRequest", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_index_endpoints of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.pagers.ListIndexEndpointsAsyncPager"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.list_locations", "name": "list_locations", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_locations of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.list_operations", "name": "list_operations", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_operations of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mutate_deployed_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "request", "index_endpoint", "deployed_index", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.mutate_deployed_index", "name": "mutate_deployed_index", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "request", "index_endpoint", "deployed_index", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.types.index_endpoint_service.MutateDeployedIndexRequest", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.types.index_endpoint.DeployedIndex", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mutate_deployed_index of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.api_core.operation_async.AsyncOperation"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_common_billing_account_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.parse_common_billing_account_path", "name": "parse_common_billing_account_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "parse_common_folder_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.parse_common_folder_path", "name": "parse_common_folder_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "parse_common_location_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.parse_common_location_path", "name": "parse_common_location_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "parse_common_organization_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.parse_common_organization_path", "name": "parse_common_organization_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "parse_common_project_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.parse_common_project_path", "name": "parse_common_project_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "parse_index_endpoint_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.parse_index_endpoint_path", "name": "parse_index_endpoint_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "parse_index_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.parse_index_path", "name": "parse_index_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "parse_reservation_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.parse_reservation_path", "name": "parse_reservation_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "reservation_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.reservation_path", "name": "reservation_path", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0, 0, 0], "arg_names": ["project_id_or_number", "zone", "reservation_name"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "imprecise_arg_kinds": false, "variables": []}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "set_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.set_iam_policy", "name": "set_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_iam_policy of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_iam_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.test_iam_permissions", "name": "test_iam_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_iam_permissions of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.transport", "name": "transport", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transport of IndexEndpointServiceAsyncClient", "ret_type": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.transports.base.IndexEndpointServiceTransport", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.transport", "name": "transport", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transport of IndexEndpointServiceAsyncClient", "ret_type": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.transports.base.IndexEndpointServiceTransport", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "undeploy_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "request", "index_endpoint", "deployed_index_id", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.undeploy_index", "name": "undeploy_index", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "request", "index_endpoint", "deployed_index_id", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.types.index_endpoint_service.UndeployIndexRequest", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "undeploy_index of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.api_core.operation_async.AsyncOperation"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "universe_domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.universe_domain", "name": "universe_domain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "universe_domain of IndexEndpointServiceAsyncClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.universe_domain", "name": "universe_domain", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "universe_domain of IndexEndpointServiceAsyncClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update_index_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "request", "index_endpoint", "update_mask", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.update_index_endpoint", "name": "update_index_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "request", "index_endpoint", "update_mask", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.types.index_endpoint_service.UpdateIndexEndpointRequest", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.types.index_endpoint.IndexEndpoint", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.field_mask_pb2", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_index_endpoint of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.index_endpoint.IndexEndpoint"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wait_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.wait_operation", "name": "wait_operation", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "wait_operation of IndexEndpointServiceAsyncClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IndexEndpointServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.client.IndexEndpointServiceClient", "kind": "Gdef", "module_public": false}, "IndexEndpointServiceGrpcAsyncIOTransport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.transports.grpc_asyncio.IndexEndpointServiceGrpcAsyncIOTransport", "kind": "Gdef", "module_public": false}, "IndexEndpointServiceTransport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.transports.base.IndexEndpointServiceTransport", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef", "module_public": false}, "MutableSequence": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSequence", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "OptionalRetry": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.OptionalRetry", "line": 42, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.api_core.retry.retry_unary_async.AsyncRetry", "google.api_core.gapic_v1.method._MethodDefault", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "core_exceptions": {".class": "SymbolTableNode", "cross_ref": "google.api_core.exceptions", "kind": "Gdef", "module_public": false}, "empty_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.empty_pb2", "name": "empty_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.empty_pb2", "source_any": null, "type_of_any": 3}}}, "encryption_spec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.encryption_spec", "kind": "Gdef", "module_public": false}, "field_mask_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.field_mask_pb2", "name": "field_mask_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.field_mask_pb2", "source_any": null, "type_of_any": 3}}}, "ga_credentials": {".class": "SymbolTableNode", "cross_ref": "google.auth.credentials", "kind": "Gdef", "module_public": false}, "gac_operation": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operation", "kind": "Gdef", "module_public": false}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef", "module_public": false}, "gca_index_endpoint": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint", "kind": "Gdef", "module_public": false}, "gca_operation": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.operation", "kind": "Gdef", "module_public": false}, "iam_policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.iam_policy_pb2", "name": "iam_policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.iam_policy_pb2", "source_any": null, "type_of_any": 3}}}, "index_endpoint": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint", "kind": "Gdef", "module_public": false}, "index_endpoint_service": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service", "kind": "Gdef", "module_public": false}, "locations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.locations_pb2", "name": "locations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.locations_pb2", "source_any": null, "type_of_any": 3}}}, "operation_async": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operation_async", "kind": "Gdef", "module_public": false}, "operations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.operations_pb2", "name": "operations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.operations_pb2", "source_any": null, "type_of_any": 3}}}, "package_version": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.gapic_version", "kind": "Gdef", "module_public": false}, "pagers": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.pagers", "kind": "Gdef", "module_public": false}, "policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.policy_pb2", "name": "policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.policy_pb2", "source_any": null, "type_of_any": 3}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "retries": {".class": "SymbolTableNode", "cross_ref": "google.api_core.retry_async", "kind": "Gdef", "module_public": false}, "service_account": {".class": "SymbolTableNode", "cross_ref": "google.oauth2.service_account", "kind": "Gdef", "module_public": false}, "service_networking": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.service_networking", "kind": "Gdef", "module_public": false}, "timestamp_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.timestamp_pb2", "name": "timestamp_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.timestamp_pb2", "source_any": null, "type_of_any": 3}}}}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/index_endpoint_service/async_client.py"}