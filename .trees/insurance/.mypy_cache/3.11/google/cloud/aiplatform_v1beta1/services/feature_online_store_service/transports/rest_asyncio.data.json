{".class": "MypyFile", "_fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncAuthorizedSession": {".class": "SymbolTableNode", "cross_ref": "google.auth.aio.transport.sessions.AsyncAuthorizedSession", "kind": "Gdef"}, "AsyncFeatureOnlineStoreServiceRestInterceptor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", "name": "AsyncFeatureOnlineStoreServiceRestInterceptor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", "builtins.object"], "names": {".class": "SymbolTable", "post_cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.post_cancel_operation", "name": "post_cancel_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "NoneType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_cancel_operation of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.post_delete_operation", "name": "post_delete_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "NoneType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_delete_operation of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_fetch_feature_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.post_fetch_feature_values", "name": "post_fetch_feature_values", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.FetchFeatureValuesResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_fetch_feature_values of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.FetchFeatureValuesResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.post_get_iam_policy", "name": "post_get_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_iam_policy of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.post_get_location", "name": "post_get_location", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_location of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.post_get_operation", "name": "post_get_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_operation of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_list_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.post_list_locations", "name": "post_list_locations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_list_locations of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.post_list_operations", "name": "post_list_operations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_list_operations of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_search_nearest_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.post_search_nearest_entities", "name": "post_search_nearest_entities", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.SearchNearestEntitiesResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_search_nearest_entities of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.SearchNearestEntitiesResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_set_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.post_set_iam_policy", "name": "post_set_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_set_iam_policy of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_test_iam_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.post_test_iam_permissions", "name": "post_test_iam_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_test_iam_permissions of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_wait_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.post_wait_operation", "name": "post_wait_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_wait_operation of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.pre_cancel_operation", "name": "pre_cancel_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_cancel_operation of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.pre_delete_operation", "name": "pre_delete_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_delete_operation of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_fetch_feature_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.pre_fetch_feature_values", "name": "pre_fetch_feature_values", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.FetchFeatureValuesRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_fetch_feature_values of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.feature_online_store_service.FetchFeatureValuesRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.pre_get_iam_policy", "name": "pre_get_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_iam_policy of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.pre_get_location", "name": "pre_get_location", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_location of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.pre_get_operation", "name": "pre_get_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_operation of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_list_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.pre_list_locations", "name": "pre_list_locations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_list_locations of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.pre_list_operations", "name": "pre_list_operations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_list_operations of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_search_nearest_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.pre_search_nearest_entities", "name": "pre_search_nearest_entities", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.SearchNearestEntitiesRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_search_nearest_entities of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.feature_online_store_service.SearchNearestEntitiesRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_set_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.pre_set_iam_policy", "name": "pre_set_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_set_iam_policy of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_test_iam_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.pre_test_iam_permissions", "name": "pre_test_iam_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_test_iam_permissions of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_wait_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.pre_wait_operation", "name": "pre_wait_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_wait_operation of AsyncFeatureOnlineStoreServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncFeatureOnlineStoreServiceRestStub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "name": "AsyncFeatureOnlineStoreServiceRestStub", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 389, "name": "_session", "type": "google.auth.aio.transport.sessions.AsyncAuthorizedSession"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 390, "name": "_host", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 391, "name": "_interceptor", "type": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_session", "_host", "_interceptor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_session", "_host", "_interceptor"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "google.auth.aio.transport.sessions.AsyncAuthorizedSession", "builtins.str", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFeatureOnlineStoreServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_session"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "_host"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "_interceptor"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "arg_types": ["google.auth.aio.transport.sessions.AsyncAuthorizedSession", "builtins.str", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of AsyncFeatureOnlineStoreServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "arg_types": ["google.auth.aio.transport.sessions.AsyncAuthorizedSession", "builtins.str", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of AsyncFeatureOnlineStoreServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub._host", "name": "_host", "setter_type": null, "type": "builtins.str"}}, "_interceptor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub._interceptor", "name": "_interceptor", "setter_type": null, "type": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor"}}, "_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub._session", "name": "_session", "setter_type": null, "type": "google.auth.aio.transport.sessions.AsyncAuthorizedSession"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncFeatureOnlineStoreServiceRestTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport", "name": "AsyncFeatureOnlineStoreServiceRestTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.base.FeatureOnlineStoreServiceTransport", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_CancelOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseCancelOperation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._CancelOperation", "name": "_CancelOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._CancelOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._CancelOperation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseCancelOperation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._CancelOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._CancelOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _CancelOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._CancelOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._CancelOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._CancelOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _CancelOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._CancelOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._CancelOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DeleteOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseDeleteOperation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._DeleteOperation", "name": "_DeleteOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._DeleteOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._DeleteOperation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseDeleteOperation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._DeleteOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._DeleteOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _DeleteOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._DeleteOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._DeleteOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._DeleteOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _DeleteOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._DeleteOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._DeleteOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FetchFeatureValues": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseFetchFeatureValues", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._FetchFeatureValues", "name": "_FetchFeatureV<PERSON>ues", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._FetchFeatureValues", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._FetchFeatureValues", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseFetchFeatureValues", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._FetchFeatureValues.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._FetchFeatureValues", "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.FetchFeatureValuesRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _FetchFeatureValues", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.FetchFeatureValuesResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._FetchFeatureValues.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._FetchFeatureValues._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._FetchFeatureValues._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _FetchFeatureValues", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._FetchFeatureValues.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._FetchFeatureValues", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetIamPolicy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseGetIamPolicy", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetIamPolicy", "name": "_GetIamPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetIamPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetIamPolicy", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseGetIamPolicy", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetIamPolicy.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetIamPolicy", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetIamPolicy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetIamPolicy.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetIamPolicy._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetIamPolicy._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetIamPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetIamPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetLocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseGetLocation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetLocation", "name": "_GetLocation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetLocation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetLocation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseGetLocation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetLocation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetLocation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetLocation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetLocation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetLocation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetLocation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetLocation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetLocation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetLocation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseGetOperation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetOperation", "name": "_GetOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetOperation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseGetOperation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._GetOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ListLocations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseListLocations", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListLocations", "name": "_ListLocations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListLocations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListLocations", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseListLocations", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListLocations.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListLocations", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _ListLocations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListLocations.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListLocations._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListLocations._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _ListLocations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListLocations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListLocations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ListOperations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseListOperations", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListOperations", "name": "_ListOperations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListOperations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListOperations", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseListOperations", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListOperations.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListOperations", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _ListOperations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListOperations.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListOperations._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListOperations._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _ListOperations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListOperations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._ListOperations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SearchNearestEntities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseSearchNearestEntities", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SearchNearestEntities", "name": "_SearchNearestEntities", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SearchNearestEntities", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SearchNearestEntities", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseSearchNearestEntities", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SearchNearestEntities.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SearchNearestEntities", "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.SearchNearestEntitiesRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _SearchNearestEntities", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.SearchNearestEntitiesResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SearchNearestEntities.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SearchNearestEntities._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SearchNearestEntities._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _SearchNearestEntities", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SearchNearestEntities.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SearchNearestEntities", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SetIamPolicy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseSetIamPolicy", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SetIamPolicy", "name": "_SetIamPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SetIamPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SetIamPolicy", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseSetIamPolicy", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SetIamPolicy.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SetIamPolicy", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _SetIamPolicy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SetIamPolicy.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SetIamPolicy._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SetIamPolicy._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _SetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SetIamPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._SetIamPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_StreamingFetchFeatureValues": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseStreamingFetchFeatureValues", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._StreamingFetchFeatureValues", "name": "_StreamingFetchFeatureValues", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._StreamingFetchFeatureValues", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._StreamingFetchFeatureValues", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseStreamingFetchFeatureValues", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._StreamingFetchFeatureValues.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._StreamingFetchFeatureValues", "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.StreamingFetchFeatureValuesRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _StreamingFetchFeatureValues", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.api_core.rest_streaming_async.AsyncResponseIterator"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._StreamingFetchFeatureValues.__hash__", "name": "__hash__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._StreamingFetchFeatureValues.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._StreamingFetchFeatureValues", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_TestIamPermissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseTestIamPermissions", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._TestIamPermissions", "name": "_TestIamPermissions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._TestIamPermissions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._TestIamPermissions", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseTestIamPermissions", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._TestIamPermissions.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._TestIamPermissions", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _TestIamPermissions", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._TestIamPermissions.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._TestIamPermissions._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._TestIamPermissions._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _TestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._TestIamPermissions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._TestIamPermissions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WaitOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseWaitOperation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._WaitOperation", "name": "_WaitOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._WaitOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._WaitOperation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport._BaseWaitOperation", "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._WaitOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._WaitOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _WaitOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._WaitOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._WaitOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._WaitOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _WaitOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._WaitOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._WaitOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "url_scheme", "interceptor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "url_scheme", "interceptor"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport", "builtins.str", {".class": "UnionType", "items": ["google.auth.aio.credentials.Credentials", {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.api_core.gapic_v1.client_info.ClientInfo", "builtins.str", {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_interceptor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._interceptor", "name": "_interceptor", "setter_type": null, "type": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestInterceptor"}}, "_prep_wrapped_messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "client_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._prep_wrapped_messages", "name": "_prep_wrapped_messages", "type": null}}, "_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._session", "name": "_session", "setter_type": null, "type": "google.auth.aio.transport.sessions.AsyncAuthorizedSession"}}, "_wrap_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "func", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._wrap_method", "name": "_wrap_method", "type": null}}, "_wrap_with_kind": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport._wrap_with_kind", "name": "_wrap_with_kind", "setter_type": null, "type": "builtins.bool"}}, "cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.cancel_operation", "name": "cancel_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.cancel_operation", "name": "cancel_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cancel_operation of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.close", "name": "close", "type": null}}, "delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.delete_operation", "name": "delete_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.delete_operation", "name": "delete_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_operation of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fetch_feature_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.fetch_feature_values", "name": "fetch_feature_values", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fetch_feature_values of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_online_store_service.FetchFeatureValuesRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.FetchFeatureValuesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.fetch_feature_values", "name": "fetch_feature_values", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fetch_feature_values of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_online_store_service.FetchFeatureValuesRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.FetchFeatureValuesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.get_iam_policy", "name": "get_iam_policy", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.get_iam_policy", "name": "get_iam_policy", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_iam_policy of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.get_location", "name": "get_location", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.get_location", "name": "get_location", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_location of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.get_operation", "name": "get_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.get_operation", "name": "get_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_operation of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.kind", "name": "kind", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kind of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.kind", "name": "kind", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kind of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.list_locations", "name": "list_locations", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.list_locations", "name": "list_locations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_locations of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.list_operations", "name": "list_operations", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.list_operations", "name": "list_operations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_operations of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "search_nearest_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.search_nearest_entities", "name": "search_nearest_entities", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "search_nearest_entities of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_online_store_service.SearchNearestEntitiesRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.SearchNearestEntitiesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.search_nearest_entities", "name": "search_nearest_entities", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "search_nearest_entities of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_online_store_service.SearchNearestEntitiesRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.SearchNearestEntitiesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.set_iam_policy", "name": "set_iam_policy", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.set_iam_policy", "name": "set_iam_policy", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_iam_policy of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "streaming_fetch_feature_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.streaming_fetch_feature_values", "name": "streaming_fetch_feature_values", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "streaming_fetch_feature_values of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_online_store_service.StreamingFetchFeatureValuesRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.StreamingFetchFeatureValuesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.streaming_fetch_feature_values", "name": "streaming_fetch_feature_values", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "streaming_fetch_feature_values of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_online_store_service.StreamingFetchFeatureValuesRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.StreamingFetchFeatureValuesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_iam_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.test_iam_permissions", "name": "test_iam_permissions", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.test_iam_permissions", "name": "test_iam_permissions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_iam_permissions of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "wait_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.wait_operation", "name": "wait_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.wait_operation", "name": "wait_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "wait_operation of AsyncFeatureOnlineStoreServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.AsyncFeatureOnlineStoreServiceRestTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncOperationsRestClient": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1.operations_rest_client_async.AsyncOperationsRestClient", "kind": "Gdef"}, "BASE_DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.base.DEFAULT_CLIENT_INFO", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.DEFAULT_CLIENT_INFO", "name": "DEFAULT_CLIENT_INFO", "setter_type": null, "type": "google.api_core.gapic_v1.client_info.ClientInfo"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OptionalRetry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.OptionalRetry", "line": 60, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.api_core.retry.retry_unary_async.AsyncRetry", "google.api_core.gapic_v1.method._MethodDefault", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_BaseFeatureOnlineStoreServiceRestTransport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_base._BaseFeatureOnlineStoreServiceRestTransport", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "aiohttp": {".class": "SymbolTableNode", "cross_ref": "aiohttp", "kind": "Gdef"}, "core_exceptions": {".class": "SymbolTableNode", "cross_ref": "google.api_core.exceptions", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.e", "name": "e", "setter_type": null, "type": {".class": "DeletedType", "source": "e"}}}, "feature_online_store_service": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service", "kind": "Gdef"}, "ga_credentials_async": {".class": "SymbolTableNode", "cross_ref": "google.auth.aio.credentials", "kind": "Gdef"}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef"}, "google": {".class": "SymbolTableNode", "cross_ref": "google", "kind": "Gdef"}, "iam_policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.iam_policy_pb2", "name": "iam_policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "json_format": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.json_format", "name": "json_format", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.json_format", "source_any": null, "type_of_any": 3}}}, "locations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.locations_pb2", "name": "locations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}}}, "operations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "name": "operations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}}}, "policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.policy_pb2", "name": "policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}}}, "rest_helpers": {".class": "SymbolTableNode", "cross_ref": "google.api_core.rest_helpers", "kind": "Gdef"}, "rest_streaming_async": {".class": "SymbolTableNode", "cross_ref": "google.api_core.rest_streaming_async", "kind": "Gdef"}, "retries": {".class": "SymbolTableNode", "cross_ref": "google.api_core.retry_async", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/feature_online_store_service/transports/rest_asyncio.py"}