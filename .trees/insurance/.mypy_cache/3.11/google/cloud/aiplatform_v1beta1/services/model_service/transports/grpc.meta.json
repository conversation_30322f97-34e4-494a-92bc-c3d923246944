{"data_mtime": 1755541229, "dep_lines": [40, 28, 30, 34, 35, 24, 28, 19, 20, 21, 23, 19, 22, 16, 17, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 36, 37, 39, 26], "dep_prios": [5, 10, 10, 10, 10, 5, 20, 10, 10, 10, 10, 20, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 10], "dependencies": ["google.cloud.aiplatform_v1beta1.services.model_service.transports.base", "google.cloud.aiplatform_v1beta1.types.model", "google.cloud.aiplatform_v1beta1.types.model_evaluation", "google.cloud.aiplatform_v1beta1.types.model_evaluation_slice", "google.cloud.aiplatform_v1beta1.types.model_service", "google.auth.transport.grpc", "google.cloud.aiplatform_v1beta1.types", "google.api_core.grpc_helpers", "google.api_core.operations_v1", "google.api_core.gapic_v1", "google.auth.credentials", "google.api_core", "google.auth", "warnings", "typing", "google", "builtins", "_frozen_importlib", "_warnings", "abc", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.operations_v1.operations_client", "google.auth._credentials_base", "google.auth.transport", "types"], "hash": "6171fe97254463a9cdf7e8810aea69c23f0f6f56", "id": "google.cloud.aiplatform_v1beta1.services.model_service.transports.grpc", "ignore_all": true, "interface_hash": "f32cba99eadde54f74df460185db3e26a4661f3a", "mtime": 1753678821, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/model_service/transports/grpc.py", "plugin_data": null, "size": 44666, "suppressed": ["google.cloud.location", "google.iam.v1", "google.longrunning", "grpc"], "version_id": "1.17.1"}