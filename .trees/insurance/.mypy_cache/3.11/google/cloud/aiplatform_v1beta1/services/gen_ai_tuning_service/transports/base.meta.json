{"data_mtime": 1755541228, "dep_lines": [30, 31, 19, 30, 19, 23, 24, 25, 26, 27, 28, 21, 22, 28, 16, 17, 21, 1, 1, 1, 1, 1, 1, 1, 1, 33, 34, 36, 37], "dep_prios": [10, 10, 10, 20, 20, 10, 10, 10, 10, 10, 10, 10, 10, 20, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5], "dependencies": ["google.cloud.aiplatform_v1beta1.types.genai_tuning_service", "google.cloud.aiplatform_v1beta1.types.tuning_job", "google.cloud.aiplatform_v1beta1.gapic_version", "google.cloud.aiplatform_v1beta1.types", "google.cloud.aiplatform_v1beta1", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.retry", "google.api_core.operations_v1", "google.auth.credentials", "google.oauth2.service_account", "google.auth", "google.api_core", "google.oauth2", "abc", "typing", "google", "builtins", "_frozen_importlib", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.auth._credentials_base", "google.auth._default", "types"], "hash": "2298ac7066088422fd092e61f5dbc92b679d3bf3", "id": "google.cloud.aiplatform_v1beta1.services.gen_ai_tuning_service.transports.base", "ignore_all": true, "interface_hash": "734d9eb132ece98e6ad12e93dcfe6429741a68a7", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/gen_ai_tuning_service/transports/base.py", "plugin_data": null, "size": 13108, "suppressed": ["google.cloud.location", "google.iam.v1", "google.longrunning", "google.protobuf"], "version_id": "1.17.1"}