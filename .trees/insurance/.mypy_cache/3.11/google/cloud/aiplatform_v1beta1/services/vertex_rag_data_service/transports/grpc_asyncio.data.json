{".class": "MypyFile", "_fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.base.DEFAULT_CLIENT_INFO", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "SslCredentials": {".class": "SymbolTableNode", "cross_ref": "google.auth.transport.grpc.SslCredentials", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "VertexRagDataServiceGrpcAsyncIOTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.base.VertexRagDataServiceTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport", "name": "VertexRagDataServiceGrpcAsyncIOTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport", "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.base.VertexRagDataServiceTransport", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "credentials_file", "scopes", "channel", "api_mtls_endpoint", "client_cert_source", "ssl_channel_credentials", "client_cert_source_for_mtls", "quota_project_id", "client_info", "always_use_jwt_access", "api_audience"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "credentials_file", "scopes", "channel", "api_mtls_endpoint", "client_cert_source", "ssl_channel_credentials", "client_cert_source_for_mtls", "quota_project_id", "client_info", "always_use_jwt_access", "api_audience"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport", "builtins.str", {".class": "UnionType", "items": ["google.auth.credentials.Credentials", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.aio", "source_any": null, "type_of_any": 3}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.aio", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.grpc", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.api_core.gapic_v1.client_info.ClientInfo", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_grpc_channel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport._grpc_channel", "name": "_grpc_channel", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.aio", "source_any": null, "type_of_any": 3}}}, "_operations_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport._operations_client", "name": "_operations_client", "setter_type": null, "type": {".class": "UnionType", "items": ["google.api_core.operations_v1.operations_async_client.OperationsAsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_prep_wrapped_messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "client_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport._prep_wrapped_messages", "name": "_prep_wrapped_messages", "type": null}}, "_ssl_channel_credentials": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport._ssl_channel_credentials", "name": "_ssl_channel_credentials", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.grpc", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_stubs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport._stubs", "name": "_stubs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_wrap_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "func", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport._wrap_method", "name": "_wrap_method", "type": null}}, "_wrap_with_kind": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport._wrap_with_kind", "name": "_wrap_with_kind", "setter_type": null, "type": "builtins.bool"}}, "cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.cancel_operation", "name": "cancel_operation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cancel_operation of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.cancel_operation", "name": "cancel_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cancel_operation of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.close", "name": "close", "type": null}}, "create_channel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "host", "credentials", "credentials_file", "scopes", "quota_project_id", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.create_channel", "name": "create_channel", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "host", "credentials", "credentials_file", "scopes", "quota_project_id", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"}, "builtins.str", {".class": "UnionType", "items": ["google.auth.credentials.Credentials", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_channel of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.aio", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.create_channel", "name": "create_channel", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "host", "credentials", "credentials_file", "scopes", "quota_project_id", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"}, "builtins.str", {".class": "UnionType", "items": ["google.auth.credentials.Credentials", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_channel of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.aio", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_rag_corpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.create_rag_corpus", "name": "create_rag_corpus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_rag_corpus of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.CreateRagCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.create_rag_corpus", "name": "create_rag_corpus", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_rag_corpus of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.CreateRagCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.delete_operation", "name": "delete_operation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_operation of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.delete_operation", "name": "delete_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_operation of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_rag_corpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.delete_rag_corpus", "name": "delete_rag_corpus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_rag_corpus of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.DeleteRagCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.delete_rag_corpus", "name": "delete_rag_corpus", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_rag_corpus of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.DeleteRagCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_rag_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.delete_rag_file", "name": "delete_rag_file", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_rag_file of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.DeleteRagFileRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.delete_rag_file", "name": "delete_rag_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_rag_file of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.DeleteRagFileRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.get_iam_policy", "name": "get_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_iam_policy of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.get_iam_policy", "name": "get_iam_policy", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_iam_policy of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.get_location", "name": "get_location", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_location of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.get_location", "name": "get_location", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_location of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.get_operation", "name": "get_operation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_operation of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.get_operation", "name": "get_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_operation of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_rag_corpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.get_rag_corpus", "name": "get_rag_corpus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_rag_corpus of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.GetRagCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data.RagCorpus"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.get_rag_corpus", "name": "get_rag_corpus", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_rag_corpus of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.GetRagCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data.RagCorpus"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_rag_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.get_rag_file", "name": "get_rag_file", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_rag_file of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.GetRagFileRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data.RagFile"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.get_rag_file", "name": "get_rag_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_rag_file of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.GetRagFileRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data.RagFile"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "grpc_channel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.grpc_channel", "name": "grpc_channel", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "grpc_channel of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.aio", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.grpc_channel", "name": "grpc_channel", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "grpc_channel of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.aio", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "import_rag_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.import_rag_files", "name": "import_rag_files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "import_rag_files of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ImportRagFilesRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.import_rag_files", "name": "import_rag_files", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "import_rag_files of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ImportRagFilesRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.kind", "name": "kind", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kind of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.kind", "name": "kind", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kind of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.list_locations", "name": "list_locations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_locations of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.list_locations", "name": "list_locations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_locations of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.list_operations", "name": "list_operations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_operations of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.list_operations", "name": "list_operations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_operations of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_rag_corpora": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.list_rag_corpora", "name": "list_rag_corpora", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_rag_corpora of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ListRagCorporaRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ListRagCorporaResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.list_rag_corpora", "name": "list_rag_corpora", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_rag_corpora of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ListRagCorporaRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ListRagCorporaResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_rag_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.list_rag_files", "name": "list_rag_files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_rag_files of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ListRagFilesRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ListRagFilesResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.list_rag_files", "name": "list_rag_files", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_rag_files of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ListRagFilesRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ListRagFilesResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "operations_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.operations_client", "name": "operations_client", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "operations_client of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": "google.api_core.operations_v1.operations_async_client.OperationsAsyncClient", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.operations_client", "name": "operations_client", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "operations_client of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": "google.api_core.operations_v1.operations_async_client.OperationsAsyncClient", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.set_iam_policy", "name": "set_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_iam_policy of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.set_iam_policy", "name": "set_iam_policy", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_iam_policy of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_iam_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.test_iam_permissions", "name": "test_iam_permissions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_iam_permissions of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.test_iam_permissions", "name": "test_iam_permissions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_iam_permissions of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update_rag_corpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.update_rag_corpus", "name": "update_rag_corpus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_rag_corpus of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.UpdateRagCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.update_rag_corpus", "name": "update_rag_corpus", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_rag_corpus of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.UpdateRagCorpusRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "upload_rag_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.upload_rag_file", "name": "upload_rag_file", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upload_rag_file of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.UploadRagFileRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.UploadRagFileResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.upload_rag_file", "name": "upload_rag_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upload_rag_file of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.UploadRagFileRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.UploadRagFileResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "wait_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.wait_operation", "name": "wait_operation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "wait_operation of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.wait_operation", "name": "wait_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "wait_operation of VertexRagDataServiceGrpcAsyncIOTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.VertexRagDataServiceGrpcAsyncIOTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VertexRagDataServiceGrpcTransport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc.VertexRagDataServiceGrpcTransport", "kind": "Gdef", "module_public": false}, "VertexRagDataServiceTransport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.base.VertexRagDataServiceTransport", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "aio": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.aio", "name": "aio", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.aio", "source_any": null, "type_of_any": 3}}}, "core_exceptions": {".class": "SymbolTableNode", "cross_ref": "google.api_core.exceptions", "kind": "Gdef", "module_public": false}, "ga_credentials": {".class": "SymbolTableNode", "cross_ref": "google.auth.credentials", "kind": "Gdef", "module_public": false}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef", "module_public": false}, "grpc": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.grpc", "name": "grpc", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.grpc", "source_any": null, "type_of_any": 3}}}, "grpc_helpers_async": {".class": "SymbolTableNode", "cross_ref": "google.api_core.grpc_helpers_async", "kind": "Gdef", "module_public": false}, "iam_policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.iam_policy_pb2", "name": "iam_policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef", "module_public": false}, "locations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.locations_pb2", "name": "locations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}}}, "operations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "name": "operations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}}}, "operations_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1", "kind": "Gdef", "module_public": false}, "policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.policy_pb2", "name": "policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.transports.grpc_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}}}, "retries": {".class": "SymbolTableNode", "cross_ref": "google.api_core.retry_async", "kind": "Gdef", "module_public": false}, "vertex_rag_data": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data", "kind": "Gdef", "module_public": false}, "vertex_rag_data_service": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/vertex_rag_data_service/transports/grpc_asyncio.py"}