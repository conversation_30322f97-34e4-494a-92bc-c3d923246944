{".class": "MypyFile", "_fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncAuthorizedSession": {".class": "SymbolTableNode", "cross_ref": "google.auth.aio.transport.sessions.AsyncAuthorizedSession", "kind": "Gdef"}, "AsyncGenAiCacheServiceRestInterceptor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", "name": "AsyncGenAiCacheServiceRestInterceptor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", "builtins.object"], "names": {".class": "SymbolTable", "post_cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.post_cancel_operation", "name": "post_cancel_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "NoneType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_cancel_operation of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_create_cached_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.post_create_cached_content", "name": "post_create_cached_content", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_create_cached_content of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.post_delete_operation", "name": "post_delete_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "NoneType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_delete_operation of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_cached_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.post_get_cached_content", "name": "post_get_cached_content", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_cached_content of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.post_get_iam_policy", "name": "post_get_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_iam_policy of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.post_get_location", "name": "post_get_location", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_location of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.post_get_operation", "name": "post_get_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_operation of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_list_cached_contents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.post_list_cached_contents", "name": "post_list_cached_contents", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.ListCachedContentsResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_list_cached_contents of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.ListCachedContentsResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_list_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.post_list_locations", "name": "post_list_locations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_list_locations of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.post_list_operations", "name": "post_list_operations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_list_operations of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_set_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.post_set_iam_policy", "name": "post_set_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_set_iam_policy of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_test_iam_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.post_test_iam_permissions", "name": "post_test_iam_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_test_iam_permissions of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_update_cached_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.post_update_cached_content", "name": "post_update_cached_content", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_update_cached_content of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_wait_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.post_wait_operation", "name": "post_wait_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_wait_operation of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_cancel_operation", "name": "pre_cancel_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_cancel_operation of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_create_cached_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_create_cached_content", "name": "pre_create_cached_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.CreateCachedContentRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_create_cached_content of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.CreateCachedContentRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_delete_cached_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_delete_cached_content", "name": "pre_delete_cached_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.DeleteCachedContentRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_delete_cached_content of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.DeleteCachedContentRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_delete_operation", "name": "pre_delete_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_delete_operation of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_cached_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_get_cached_content", "name": "pre_get_cached_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.GetCachedContentRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_cached_content of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.GetCachedContentRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_get_iam_policy", "name": "pre_get_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_iam_policy of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_get_location", "name": "pre_get_location", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_location of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_get_operation", "name": "pre_get_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_operation of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_list_cached_contents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_list_cached_contents", "name": "pre_list_cached_contents", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.ListCachedContentsRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_list_cached_contents of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.ListCachedContentsRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_list_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_list_locations", "name": "pre_list_locations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_list_locations of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_list_operations", "name": "pre_list_operations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_list_operations of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_set_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_set_iam_policy", "name": "pre_set_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_set_iam_policy of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_test_iam_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_test_iam_permissions", "name": "pre_test_iam_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_test_iam_permissions of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_update_cached_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_update_cached_content", "name": "pre_update_cached_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.UpdateCachedContentRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_update_cached_content of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.UpdateCachedContentRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_wait_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.pre_wait_operation", "name": "pre_wait_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_wait_operation of AsyncGenAiCacheServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncGenAiCacheServiceRestStub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "name": "AsyncGenAiCacheServiceRestStub", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 472, "name": "_session", "type": "google.auth.aio.transport.sessions.AsyncAuthorizedSession"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 473, "name": "_host", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 474, "name": "_interceptor", "type": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_session", "_host", "_interceptor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_session", "_host", "_interceptor"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "google.auth.aio.transport.sessions.AsyncAuthorizedSession", "builtins.str", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncGenAiCacheServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_session"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "_host"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "_interceptor"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "arg_types": ["google.auth.aio.transport.sessions.AsyncAuthorizedSession", "builtins.str", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of AsyncGenAiCacheServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "arg_types": ["google.auth.aio.transport.sessions.AsyncAuthorizedSession", "builtins.str", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of AsyncGenAiCacheServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub._host", "name": "_host", "setter_type": null, "type": "builtins.str"}}, "_interceptor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub._interceptor", "name": "_interceptor", "setter_type": null, "type": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor"}}, "_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub._session", "name": "_session", "setter_type": null, "type": "google.auth.aio.transport.sessions.AsyncAuthorizedSession"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncGenAiCacheServiceRestTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport", "name": "AsyncGenAiCacheServiceRestTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.base.GenAiCacheServiceTransport", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_CancelOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseCancelOperation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CancelOperation", "name": "_CancelOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CancelOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CancelOperation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseCancelOperation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CancelOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CancelOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _CancelOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CancelOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CancelOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CancelOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _CancelOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CancelOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CancelOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CreateCachedContent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseCreateCachedContent", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CreateCachedContent", "name": "_<PERSON>reate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CreateCachedContent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CreateCachedContent", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseCreateCachedContent", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CreateCachedContent.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CreateCachedContent", "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.CreateCachedContentRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _CreateCached<PERSON>ontent", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CreateCachedContent.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CreateCachedContent._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CreateCachedContent._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CreateCachedContent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._CreateCachedContent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DeleteCachedContent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseDeleteCachedContent", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteCachedContent", "name": "_DeleteCached<PERSON><PERSON>nt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteCachedContent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteCachedContent", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseDeleteCachedContent", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteCachedContent.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteCachedContent", "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.DeleteCachedContentRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _DeleteCachedContent", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteCachedContent.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteCachedContent._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteCachedContent._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _DeleteCachedContent", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteCachedContent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteCachedContent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DeleteOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseDeleteOperation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteOperation", "name": "_DeleteOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteOperation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseDeleteOperation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _DeleteOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _DeleteOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._DeleteOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetCachedContent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseGetCachedContent", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetCachedContent", "name": "_Get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetCachedContent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetCachedContent", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseGetCachedContent", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetCachedContent.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetCachedContent", "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.GetCachedContentRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetCachedContent", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetCachedContent.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetCachedContent._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetCachedContent._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetCached<PERSON>ontent", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetCachedContent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetCachedContent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetIamPolicy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseGetIamPolicy", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetIamPolicy", "name": "_GetIamPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetIamPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetIamPolicy", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseGetIamPolicy", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetIamPolicy.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetIamPolicy", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetIamPolicy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetIamPolicy.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetIamPolicy._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetIamPolicy._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetIamPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetIamPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetLocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseGetLocation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetLocation", "name": "_GetLocation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetLocation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetLocation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseGetLocation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetLocation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetLocation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetLocation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetLocation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetLocation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetLocation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetLocation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetLocation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetLocation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseGetOperation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetOperation", "name": "_GetOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetOperation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseGetOperation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._GetOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ListCachedContents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseListCachedContents", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListCachedContents", "name": "_ListCachedContents", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListCachedContents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListCachedContents", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseListCachedContents", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListCachedContents.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListCachedContents", "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.ListCachedContentsRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _ListCachedContents", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.ListCachedContentsResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListCachedContents.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListCachedContents._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListCachedContents._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _ListCachedContents", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListCachedContents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListCachedContents", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ListLocations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseListLocations", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListLocations", "name": "_ListLocations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListLocations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListLocations", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseListLocations", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListLocations.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListLocations", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _ListLocations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListLocations.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListLocations._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListLocations._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _ListLocations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListLocations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListLocations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ListOperations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseListOperations", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListOperations", "name": "_ListOperations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListOperations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListOperations", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseListOperations", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListOperations.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListOperations", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _ListOperations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListOperations.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListOperations._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListOperations._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _ListOperations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListOperations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._ListOperations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SetIamPolicy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseSetIamPolicy", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._SetIamPolicy", "name": "_SetIamPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._SetIamPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._SetIamPolicy", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseSetIamPolicy", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._SetIamPolicy.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._SetIamPolicy", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _SetIamPolicy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._SetIamPolicy.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._SetIamPolicy._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._SetIamPolicy._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _SetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._SetIamPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._SetIamPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_TestIamPermissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseTestIamPermissions", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._TestIamPermissions", "name": "_TestIamPermissions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._TestIamPermissions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._TestIamPermissions", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseTestIamPermissions", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._TestIamPermissions.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._TestIamPermissions", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _TestIamPermissions", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._TestIamPermissions.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._TestIamPermissions._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._TestIamPermissions._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _TestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._TestIamPermissions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._TestIamPermissions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_UpdateCachedContent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseUpdateCachedContent", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._UpdateCachedContent", "name": "_UpdateC<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._UpdateCachedContent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._UpdateCachedContent", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseUpdateCachedContent", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._UpdateCachedContent.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._UpdateCachedContent", "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.UpdateCachedContentRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _UpdateCachedContent", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._UpdateCachedContent.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._UpdateCachedContent._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._UpdateCachedContent._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _UpdateCachedContent", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._UpdateCachedContent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._UpdateCachedContent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WaitOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseWaitOperation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._WaitOperation", "name": "_WaitOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._WaitOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._WaitOperation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport._BaseWaitOperation", "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._WaitOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._WaitOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _WaitOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._WaitOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._WaitOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._WaitOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _WaitOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._WaitOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._WaitOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "url_scheme", "interceptor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "url_scheme", "interceptor"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport", "builtins.str", {".class": "UnionType", "items": ["google.auth.aio.credentials.Credentials", {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.api_core.gapic_v1.client_info.ClientInfo", "builtins.str", {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_interceptor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._interceptor", "name": "_interceptor", "setter_type": null, "type": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestInterceptor"}}, "_prep_wrapped_messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "client_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._prep_wrapped_messages", "name": "_prep_wrapped_messages", "type": null}}, "_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._session", "name": "_session", "setter_type": null, "type": "google.auth.aio.transport.sessions.AsyncAuthorizedSession"}}, "_wrap_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "func", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._wrap_method", "name": "_wrap_method", "type": null}}, "_wrap_with_kind": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport._wrap_with_kind", "name": "_wrap_with_kind", "setter_type": null, "type": "builtins.bool"}}, "cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.cancel_operation", "name": "cancel_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.cancel_operation", "name": "cancel_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cancel_operation of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.close", "name": "close", "type": null}}, "create_cached_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.create_cached_content", "name": "create_cached_content", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_cached_content of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.CreateCachedContentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.create_cached_content", "name": "create_cached_content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_cached_content of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.CreateCachedContentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_cached_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.delete_cached_content", "name": "delete_cached_content", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_cached_content of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.DeleteCachedContentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.empty_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.delete_cached_content", "name": "delete_cached_content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_cached_content of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.DeleteCachedContentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.empty_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.delete_operation", "name": "delete_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.delete_operation", "name": "delete_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_operation of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_cached_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.get_cached_content", "name": "get_cached_content", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_cached_content of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.GetCachedContentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.get_cached_content", "name": "get_cached_content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_cached_content of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.GetCachedContentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.get_iam_policy", "name": "get_iam_policy", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.get_iam_policy", "name": "get_iam_policy", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_iam_policy of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.get_location", "name": "get_location", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.get_location", "name": "get_location", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_location of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.get_operation", "name": "get_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.get_operation", "name": "get_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_operation of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.kind", "name": "kind", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kind of AsyncGenAiCacheServiceRestTransport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.kind", "name": "kind", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kind of AsyncGenAiCacheServiceRestTransport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_cached_contents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.list_cached_contents", "name": "list_cached_contents", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_cached_contents of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.ListCachedContentsRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.ListCachedContentsResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.list_cached_contents", "name": "list_cached_contents", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_cached_contents of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.ListCachedContentsRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.ListCachedContentsResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.list_locations", "name": "list_locations", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.list_locations", "name": "list_locations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_locations of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.list_operations", "name": "list_operations", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.list_operations", "name": "list_operations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_operations of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.set_iam_policy", "name": "set_iam_policy", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.set_iam_policy", "name": "set_iam_policy", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_iam_policy of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_iam_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.test_iam_permissions", "name": "test_iam_permissions", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.test_iam_permissions", "name": "test_iam_permissions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_iam_permissions of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update_cached_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.update_cached_content", "name": "update_cached_content", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_cached_content of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.UpdateCachedContentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.update_cached_content", "name": "update_cached_content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_cached_content of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.UpdateCachedContentRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "wait_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.wait_operation", "name": "wait_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.wait_operation", "name": "wait_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "wait_operation of AsyncGenAiCacheServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.AsyncGenAiCacheServiceRestTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncOperationsRestClient": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1.operations_rest_client_async.AsyncOperationsRestClient", "kind": "Gdef"}, "BASE_DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.base.DEFAULT_CLIENT_INFO", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.DEFAULT_CLIENT_INFO", "name": "DEFAULT_CLIENT_INFO", "setter_type": null, "type": "google.api_core.gapic_v1.client_info.ClientInfo"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OptionalRetry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.OptionalRetry", "line": 63, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.api_core.retry.retry_unary_async.AsyncRetry", "google.api_core.gapic_v1.method._MethodDefault", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_BaseGenAiCacheServiceRestTransport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_base._BaseGenAiCacheServiceRestTransport", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "aiohttp": {".class": "SymbolTableNode", "cross_ref": "aiohttp", "kind": "Gdef"}, "cached_content": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.cached_content", "kind": "Gdef"}, "core_exceptions": {".class": "SymbolTableNode", "cross_ref": "google.api_core.exceptions", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.e", "name": "e", "setter_type": null, "type": {".class": "DeletedType", "source": "e"}}}, "empty_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.empty_pb2", "name": "empty_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.empty_pb2", "source_any": null, "type_of_any": 3}}}, "ga_credentials_async": {".class": "SymbolTableNode", "cross_ref": "google.auth.aio.credentials", "kind": "Gdef"}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef"}, "gca_cached_content": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.cached_content", "kind": "Gdef"}, "gen_ai_cache_service": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service", "kind": "Gdef"}, "google": {".class": "SymbolTableNode", "cross_ref": "google", "kind": "Gdef"}, "iam_policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.iam_policy_pb2", "name": "iam_policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "json_format": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.json_format", "name": "json_format", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.json_format", "source_any": null, "type_of_any": 3}}}, "locations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.locations_pb2", "name": "locations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}}}, "operations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "name": "operations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}}}, "policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.policy_pb2", "name": "policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}}}, "rest_helpers": {".class": "SymbolTableNode", "cross_ref": "google.api_core.rest_helpers", "kind": "Gdef"}, "rest_streaming_async": {".class": "SymbolTableNode", "cross_ref": "google.api_core.rest_streaming_async", "kind": "Gdef"}, "retries": {".class": "SymbolTableNode", "cross_ref": "google.api_core.retry_async", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/gen_ai_cache_service/transports/rest_asyncio.py"}