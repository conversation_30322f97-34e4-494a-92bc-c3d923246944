{".class": "MypyFile", "_fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncAuthorizedSession": {".class": "SymbolTableNode", "cross_ref": "google.auth.aio.transport.sessions.AsyncAuthorizedSession", "kind": "Gdef"}, "AsyncEvaluationServiceRestInterceptor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", "name": "AsyncEvaluationServiceRestInterceptor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", "builtins.object"], "names": {".class": "SymbolTable", "post_cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.post_cancel_operation", "name": "post_cancel_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "NoneType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_cancel_operation of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.post_delete_operation", "name": "post_delete_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "NoneType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_delete_operation of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_evaluate_instances": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.post_evaluate_instances", "name": "post_evaluate_instances", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.evaluation_service.EvaluateInstancesResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_evaluate_instances of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.evaluation_service.EvaluateInstancesResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.post_get_iam_policy", "name": "post_get_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_iam_policy of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.post_get_location", "name": "post_get_location", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_location of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.post_get_operation", "name": "post_get_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_operation of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_list_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.post_list_locations", "name": "post_list_locations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_list_locations of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.post_list_operations", "name": "post_list_operations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_list_operations of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_set_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.post_set_iam_policy", "name": "post_set_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_set_iam_policy of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_test_iam_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.post_test_iam_permissions", "name": "post_test_iam_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_test_iam_permissions of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_wait_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.post_wait_operation", "name": "post_wait_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_wait_operation of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.pre_cancel_operation", "name": "pre_cancel_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_cancel_operation of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.pre_delete_operation", "name": "pre_delete_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_delete_operation of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_evaluate_instances": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.pre_evaluate_instances", "name": "pre_evaluate_instances", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.evaluation_service.EvaluateInstancesRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_evaluate_instances of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.evaluation_service.EvaluateInstancesRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.pre_get_iam_policy", "name": "pre_get_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_iam_policy of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.pre_get_location", "name": "pre_get_location", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_location of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.pre_get_operation", "name": "pre_get_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_operation of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_list_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.pre_list_locations", "name": "pre_list_locations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_list_locations of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.pre_list_operations", "name": "pre_list_operations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_list_operations of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_set_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.pre_set_iam_policy", "name": "pre_set_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_set_iam_policy of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_test_iam_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.pre_test_iam_permissions", "name": "pre_test_iam_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_test_iam_permissions of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_wait_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.pre_wait_operation", "name": "pre_wait_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_wait_operation of AsyncEvaluationServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncEvaluationServiceRestStub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "name": "AsyncEvaluationServiceRestStub", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 352, "name": "_session", "type": "google.auth.aio.transport.sessions.AsyncAuthorizedSession"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 353, "name": "_host", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 354, "name": "_interceptor", "type": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_session", "_host", "_interceptor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_session", "_host", "_interceptor"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "google.auth.aio.transport.sessions.AsyncAuthorizedSession", "builtins.str", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncEvaluationServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_session"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "_host"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "_interceptor"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "arg_types": ["google.auth.aio.transport.sessions.AsyncAuthorizedSession", "builtins.str", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of AsyncEvaluationServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "arg_types": ["google.auth.aio.transport.sessions.AsyncAuthorizedSession", "builtins.str", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of AsyncEvaluationServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub._host", "name": "_host", "setter_type": null, "type": "builtins.str"}}, "_interceptor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub._interceptor", "name": "_interceptor", "setter_type": null, "type": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor"}}, "_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub._session", "name": "_session", "setter_type": null, "type": "google.auth.aio.transport.sessions.AsyncAuthorizedSession"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncEvaluationServiceRestTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport", "name": "AsyncEvaluationServiceRestTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.base.EvaluationServiceTransport", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_CancelOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseCancelOperation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._CancelOperation", "name": "_CancelOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._CancelOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._CancelOperation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseCancelOperation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._CancelOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._CancelOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _CancelOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._CancelOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._CancelOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._CancelOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _CancelOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._CancelOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._CancelOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DeleteOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseDeleteOperation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._DeleteOperation", "name": "_DeleteOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._DeleteOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._DeleteOperation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseDeleteOperation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._DeleteOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._DeleteOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _DeleteOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._DeleteOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._DeleteOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._DeleteOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _DeleteOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._DeleteOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._DeleteOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_EvaluateInstances": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseEvaluateInstances", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._EvaluateInstances", "name": "_EvaluateInstances", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._EvaluateInstances", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._EvaluateInstances", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseEvaluateInstances", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._EvaluateInstances.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._EvaluateInstances", "google.cloud.aiplatform_v1beta1.types.evaluation_service.EvaluateInstancesRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _EvaluateInstances", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.evaluation_service.EvaluateInstancesResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._EvaluateInstances.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._EvaluateInstances._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._EvaluateInstances._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _EvaluateInstances", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._EvaluateInstances.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._EvaluateInstances", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetIamPolicy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseGetIamPolicy", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetIamPolicy", "name": "_GetIamPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetIamPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetIamPolicy", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseGetIamPolicy", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetIamPolicy.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetIamPolicy", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetIamPolicy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetIamPolicy.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetIamPolicy._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetIamPolicy._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetIamPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetIamPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetLocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseGetLocation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetLocation", "name": "_GetLocation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetLocation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetLocation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseGetLocation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetLocation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetLocation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetLocation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetLocation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetLocation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetLocation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetLocation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetLocation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetLocation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseGetOperation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetOperation", "name": "_GetOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetOperation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseGetOperation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._GetOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ListLocations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseListLocations", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListLocations", "name": "_ListLocations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListLocations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListLocations", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseListLocations", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListLocations.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListLocations", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _ListLocations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListLocations.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListLocations._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListLocations._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _ListLocations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListLocations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListLocations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ListOperations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseListOperations", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListOperations", "name": "_ListOperations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListOperations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListOperations", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseListOperations", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListOperations.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListOperations", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _ListOperations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListOperations.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListOperations._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListOperations._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _ListOperations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListOperations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._ListOperations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SetIamPolicy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseSetIamPolicy", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._SetIamPolicy", "name": "_SetIamPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._SetIamPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._SetIamPolicy", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseSetIamPolicy", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._SetIamPolicy.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._SetIamPolicy", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _SetIamPolicy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._SetIamPolicy.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._SetIamPolicy._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._SetIamPolicy._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _SetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._SetIamPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._SetIamPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_TestIamPermissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseTestIamPermissions", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._TestIamPermissions", "name": "_TestIamPermissions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._TestIamPermissions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._TestIamPermissions", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseTestIamPermissions", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._TestIamPermissions.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._TestIamPermissions", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _TestIamPermissions", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._TestIamPermissions.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._TestIamPermissions._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._TestIamPermissions._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _TestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._TestIamPermissions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._TestIamPermissions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WaitOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseWaitOperation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._WaitOperation", "name": "_WaitOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._WaitOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._WaitOperation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport._BaseWaitOperation", "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._WaitOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._WaitOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _WaitOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._WaitOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._WaitOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._WaitOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _WaitOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._WaitOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._WaitOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "url_scheme", "interceptor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "url_scheme", "interceptor"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport", "builtins.str", {".class": "UnionType", "items": ["google.auth.aio.credentials.Credentials", {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.api_core.gapic_v1.client_info.ClientInfo", "builtins.str", {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncEvaluationServiceRestTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_interceptor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._interceptor", "name": "_interceptor", "setter_type": null, "type": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestInterceptor"}}, "_prep_wrapped_messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "client_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._prep_wrapped_messages", "name": "_prep_wrapped_messages", "type": null}}, "_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._session", "name": "_session", "setter_type": null, "type": "google.auth.aio.transport.sessions.AsyncAuthorizedSession"}}, "_wrap_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "func", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._wrap_method", "name": "_wrap_method", "type": null}}, "_wrap_with_kind": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport._wrap_with_kind", "name": "_wrap_with_kind", "setter_type": null, "type": "builtins.bool"}}, "cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.cancel_operation", "name": "cancel_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.cancel_operation", "name": "cancel_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cancel_operation of AsyncEvaluationServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.close", "name": "close", "type": null}}, "delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.delete_operation", "name": "delete_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.delete_operation", "name": "delete_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_operation of AsyncEvaluationServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "evaluate_instances": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.evaluate_instances", "name": "evaluate_instances", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate_instances of AsyncEvaluationServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.evaluation_service.EvaluateInstancesRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.evaluation_service.EvaluateInstancesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.evaluate_instances", "name": "evaluate_instances", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate_instances of AsyncEvaluationServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.evaluation_service.EvaluateInstancesRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.evaluation_service.EvaluateInstancesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.get_iam_policy", "name": "get_iam_policy", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.get_iam_policy", "name": "get_iam_policy", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_iam_policy of AsyncEvaluationServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.get_location", "name": "get_location", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.get_location", "name": "get_location", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_location of AsyncEvaluationServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.get_operation", "name": "get_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.get_operation", "name": "get_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_operation of AsyncEvaluationServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.kind", "name": "kind", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kind of AsyncEvaluationServiceRestTransport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.kind", "name": "kind", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kind of AsyncEvaluationServiceRestTransport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.list_locations", "name": "list_locations", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.list_locations", "name": "list_locations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_locations of AsyncEvaluationServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.list_operations", "name": "list_operations", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.list_operations", "name": "list_operations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_operations of AsyncEvaluationServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.set_iam_policy", "name": "set_iam_policy", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.set_iam_policy", "name": "set_iam_policy", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_iam_policy of AsyncEvaluationServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_iam_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.test_iam_permissions", "name": "test_iam_permissions", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.test_iam_permissions", "name": "test_iam_permissions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_iam_permissions of AsyncEvaluationServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "wait_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.wait_operation", "name": "wait_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.wait_operation", "name": "wait_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "wait_operation of AsyncEvaluationServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.AsyncEvaluationServiceRestTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncOperationsRestClient": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1.operations_rest_client_async.AsyncOperationsRestClient", "kind": "Gdef"}, "BASE_DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.base.DEFAULT_CLIENT_INFO", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.DEFAULT_CLIENT_INFO", "name": "DEFAULT_CLIENT_INFO", "setter_type": null, "type": "google.api_core.gapic_v1.client_info.ClientInfo"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OptionalRetry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.OptionalRetry", "line": 60, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.api_core.retry.retry_unary_async.AsyncRetry", "google.api_core.gapic_v1.method._MethodDefault", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_BaseEvaluationServiceRestTransport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_base._BaseEvaluationServiceRestTransport", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "aiohttp": {".class": "SymbolTableNode", "cross_ref": "aiohttp", "kind": "Gdef"}, "core_exceptions": {".class": "SymbolTableNode", "cross_ref": "google.api_core.exceptions", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.e", "name": "e", "setter_type": null, "type": {".class": "DeletedType", "source": "e"}}}, "evaluation_service": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service", "kind": "Gdef"}, "ga_credentials_async": {".class": "SymbolTableNode", "cross_ref": "google.auth.aio.credentials", "kind": "Gdef"}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef"}, "google": {".class": "SymbolTableNode", "cross_ref": "google", "kind": "Gdef"}, "iam_policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.iam_policy_pb2", "name": "iam_policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "json_format": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.json_format", "name": "json_format", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.json_format", "source_any": null, "type_of_any": 3}}}, "locations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.locations_pb2", "name": "locations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}}}, "operations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "name": "operations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}}}, "policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.policy_pb2", "name": "policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.evaluation_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}}}, "rest_helpers": {".class": "SymbolTableNode", "cross_ref": "google.api_core.rest_helpers", "kind": "Gdef"}, "rest_streaming_async": {".class": "SymbolTableNode", "cross_ref": "google.api_core.rest_streaming_async", "kind": "Gdef"}, "retries": {".class": "SymbolTableNode", "cross_ref": "google.api_core.retry_async", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/evaluation_service/transports/rest_asyncio.py"}