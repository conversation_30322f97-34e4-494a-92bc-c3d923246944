{".class": "MypyFile", "_fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncAuthorizedSession": {".class": "SymbolTableNode", "cross_ref": "google.auth.aio.transport.sessions.AsyncAuthorizedSession", "kind": "Gdef"}, "AsyncFeatureRegistryServiceRestInterceptor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "name": "AsyncFeatureRegistryServiceRestInterceptor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "builtins.object"], "names": {".class": "SymbolTable", "post_cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_cancel_operation", "name": "post_cancel_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "NoneType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_cancel_operation of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_create_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_create_feature", "name": "post_create_feature", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_create_feature of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_create_feature_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_create_feature_group", "name": "post_create_feature_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_create_feature_group of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_delete_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_delete_feature", "name": "post_delete_feature", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_delete_feature of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_delete_feature_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_delete_feature_group", "name": "post_delete_feature_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_delete_feature_group of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_delete_operation", "name": "post_delete_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "NoneType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_delete_operation of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_get_feature", "name": "post_get_feature", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.feature.Feature"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_feature of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.feature.Feature"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_feature_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_get_feature_group", "name": "post_get_feature_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.feature_group.FeatureGroup"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_feature_group of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.feature_group.FeatureGroup"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_get_iam_policy", "name": "post_get_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_iam_policy of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_get_location", "name": "post_get_location", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_location of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_get_operation", "name": "post_get_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_operation of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_list_feature_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_list_feature_groups", "name": "post_list_feature_groups", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.feature_registry_service.ListFeatureGroupsResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_list_feature_groups of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.feature_registry_service.ListFeatureGroupsResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_list_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_list_features", "name": "post_list_features", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.featurestore_service.ListFeaturesResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_list_features of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.featurestore_service.ListFeaturesResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_list_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_list_locations", "name": "post_list_locations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_list_locations of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_list_operations", "name": "post_list_operations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_list_operations of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_set_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_set_iam_policy", "name": "post_set_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_set_iam_policy of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_test_iam_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_test_iam_permissions", "name": "post_test_iam_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_test_iam_permissions of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_update_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_update_feature", "name": "post_update_feature", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_update_feature of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_update_feature_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_update_feature_group", "name": "post_update_feature_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_update_feature_group of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_wait_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.post_wait_operation", "name": "post_wait_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_wait_operation of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_cancel_operation", "name": "pre_cancel_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_cancel_operation of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_create_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_create_feature", "name": "pre_create_feature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.featurestore_service.CreateFeatureRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_create_feature of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.CreateFeatureRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_create_feature_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_create_feature_group", "name": "pre_create_feature_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.feature_registry_service.CreateFeatureGroupRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_create_feature_group of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.CreateFeatureGroupRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_delete_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_delete_feature", "name": "pre_delete_feature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.featurestore_service.DeleteFeatureRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_delete_feature of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.DeleteFeatureRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_delete_feature_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_delete_feature_group", "name": "pre_delete_feature_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.feature_registry_service.DeleteFeatureGroupRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_delete_feature_group of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.DeleteFeatureGroupRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_delete_operation", "name": "pre_delete_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_delete_operation of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_get_feature", "name": "pre_get_feature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.featurestore_service.GetFeatureRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_feature of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.GetFeatureRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_feature_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_get_feature_group", "name": "pre_get_feature_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.feature_registry_service.GetFeatureGroupRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_feature_group of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.GetFeatureGroupRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_get_iam_policy", "name": "pre_get_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_iam_policy of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_get_location", "name": "pre_get_location", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_location of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_get_operation", "name": "pre_get_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_operation of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_list_feature_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_list_feature_groups", "name": "pre_list_feature_groups", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.feature_registry_service.ListFeatureGroupsRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_list_feature_groups of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.ListFeatureGroupsRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_list_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_list_features", "name": "pre_list_features", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.featurestore_service.ListFeaturesRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_list_features of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.ListFeaturesRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_list_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_list_locations", "name": "pre_list_locations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_list_locations of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_list_operations", "name": "pre_list_operations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_list_operations of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_set_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_set_iam_policy", "name": "pre_set_iam_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_set_iam_policy of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_test_iam_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_test_iam_permissions", "name": "pre_test_iam_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_test_iam_permissions of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_update_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_update_feature", "name": "pre_update_feature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.featurestore_service.UpdateFeatureRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_update_feature of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.UpdateFeatureRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_update_feature_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_update_feature_group", "name": "pre_update_feature_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "google.cloud.aiplatform_v1beta1.types.feature_registry_service.UpdateFeatureGroupRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_update_feature_group of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.UpdateFeatureGroupRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_wait_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.pre_wait_operation", "name": "pre_wait_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_wait_operation of AsyncFeatureRegistryServiceRestInterceptor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncFeatureRegistryServiceRestStub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "name": "AsyncFeatureRegistryServiceRestStub", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 644, "name": "_session", "type": "google.auth.aio.transport.sessions.AsyncAuthorizedSession"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 645, "name": "_host", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 646, "name": "_interceptor", "type": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_session", "_host", "_interceptor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_session", "_host", "_interceptor"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "google.auth.aio.transport.sessions.AsyncAuthorizedSession", "builtins.str", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFeatureRegistryServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_session"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "_host"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "_interceptor"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "arg_types": ["google.auth.aio.transport.sessions.AsyncAuthorizedSession", "builtins.str", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of AsyncFeatureRegistryServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "arg_types": ["google.auth.aio.transport.sessions.AsyncAuthorizedSession", "builtins.str", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of AsyncFeatureRegistryServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub._host", "name": "_host", "setter_type": null, "type": "builtins.str"}}, "_interceptor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub._interceptor", "name": "_interceptor", "setter_type": null, "type": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor"}}, "_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub._session", "name": "_session", "setter_type": null, "type": "google.auth.aio.transport.sessions.AsyncAuthorizedSession"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncFeatureRegistryServiceRestTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport", "name": "AsyncFeatureRegistryServiceRestTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.base.FeatureRegistryServiceTransport", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_CancelOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCancelOperation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CancelOperation", "name": "_CancelOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CancelOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CancelOperation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCancelOperation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CancelOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CancelOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _CancelOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CancelOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CancelOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CancelOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _CancelOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CancelOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CancelOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CreateFeature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeature", "name": "_CreateFeature", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeature", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeature", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeature.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeature", "google.cloud.aiplatform_v1beta1.types.featurestore_service.CreateFeatureRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _CreateFeature", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeature.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeature._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeature._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _CreateFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeature.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeature", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CreateFeatureGroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeatureGroup", "name": "_CreateFeatureGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeatureGroup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeatureGroup", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeatureGroup.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeatureGroup", "google.cloud.aiplatform_v1beta1.types.feature_registry_service.CreateFeatureGroupRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _CreateFeatureGroup", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeatureGroup.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeatureGroup._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeatureGroup._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _CreateFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeatureGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._CreateFeatureGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DeleteFeature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeature", "name": "_DeleteFeature", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeature", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeature", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeature.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeature", "google.cloud.aiplatform_v1beta1.types.featurestore_service.DeleteFeatureRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _DeleteFeature", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeature.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeature._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeature._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _DeleteFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeature.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeature", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DeleteFeatureGroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeatureGroup", "name": "_DeleteFeatureGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeatureGroup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeatureGroup", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeatureGroup.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeatureGroup", "google.cloud.aiplatform_v1beta1.types.feature_registry_service.DeleteFeatureGroupRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _DeleteFeatureGroup", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeatureGroup.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeatureGroup._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeatureGroup._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _DeleteFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeatureGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteFeatureGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DeleteOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteOperation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteOperation", "name": "_DeleteOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteOperation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteOperation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _DeleteOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _DeleteOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._DeleteOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetFeature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeature", "name": "_GetFeature", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeature", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeature", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeature.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeature", "google.cloud.aiplatform_v1beta1.types.featurestore_service.GetFeatureRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetFeature", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.feature.Feature"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeature.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeature._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeature._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeature.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeature", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetFeatureGroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeatureGroup", "name": "_GetFeatureGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeatureGroup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeatureGroup", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeatureGroup.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeatureGroup", "google.cloud.aiplatform_v1beta1.types.feature_registry_service.GetFeatureGroupRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetFeatureGroup", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.feature_group.FeatureGroup"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeatureGroup.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeatureGroup._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeatureGroup._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeatureGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetFeatureGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetIamPolicy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetIamPolicy", "name": "_GetIamPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetIamPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetIamPolicy", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetIamPolicy.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetIamPolicy", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetIamPolicy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetIamPolicy.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetIamPolicy._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetIamPolicy._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetIamPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetIamPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetLocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetLocation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetLocation", "name": "_GetLocation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetLocation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetLocation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetLocation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetLocation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetLocation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetLocation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetLocation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetLocation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetLocation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetLocation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetLocation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetLocation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetOperation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetOperation", "name": "_GetOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetOperation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetOperation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._GetOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ListFeatureGroups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatureGroups", "name": "_ListFeatureGroups", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatureGroups", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatureGroups", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatureGroups.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatureGroups", "google.cloud.aiplatform_v1beta1.types.feature_registry_service.ListFeatureGroupsRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _ListFeatureGroups", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.feature_registry_service.ListFeatureGroupsResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatureGroups.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatureGroups._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatureGroups._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _ListFeatureGroups", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatureGroups.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatureGroups", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ListFeatures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatures", "name": "_ListFeatures", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatures", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatures", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatures.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatures", "google.cloud.aiplatform_v1beta1.types.featurestore_service.ListFeaturesRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _ListFeatures", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.cloud.aiplatform_v1beta1.types.featurestore_service.ListFeaturesResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatures.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatures._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatures._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _ListFeatures", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatures.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListFeatures", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ListLocations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListLocations", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListLocations", "name": "_ListLocations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListLocations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListLocations", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListLocations", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListLocations.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListLocations", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _ListLocations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListLocations.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListLocations._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListLocations._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _ListLocations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListLocations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListLocations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ListOperations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListOperations", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListOperations", "name": "_ListOperations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListOperations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListOperations", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListOperations", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListOperations.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListOperations", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _ListOperations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListOperations.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListOperations._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListOperations._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _ListOperations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListOperations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._ListOperations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SetIamPolicy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._SetIamPolicy", "name": "_SetIamPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._SetIamPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._SetIamPolicy", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._SetIamPolicy.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._SetIamPolicy", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _SetIamPolicy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._SetIamPolicy.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._SetIamPolicy._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._SetIamPolicy._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _SetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._SetIamPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._SetIamPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_TestIamPermissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._TestIamPermissions", "name": "_TestIamPermissions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._TestIamPermissions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._TestIamPermissions", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._TestIamPermissions.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._TestIamPermissions", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _TestIamPermissions", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._TestIamPermissions.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._TestIamPermissions._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._TestIamPermissions._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _TestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._TestIamPermissions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._TestIamPermissions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_UpdateFeature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeature", "name": "_UpdateFeature", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeature", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeature", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeature.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeature", "google.cloud.aiplatform_v1beta1.types.featurestore_service.UpdateFeatureRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _UpdateFeature", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeature.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeature._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeature._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _UpdateFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeature.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeature", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_UpdateFeatureGroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeatureGroup", "name": "_UpdateFeatureGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeatureGroup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeatureGroup", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeatureGroup.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeatureGroup", "google.cloud.aiplatform_v1beta1.types.feature_registry_service.UpdateFeatureGroupRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _UpdateFeatureGroup", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeatureGroup.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeatureGroup._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeatureGroup._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _UpdateFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeatureGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._UpdateFeatureGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WaitOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseWaitOperation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._WaitOperation", "name": "_WaitOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._WaitOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._WaitOperation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseWaitOperation", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._WaitOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._WaitOperation", {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _WaitOperation", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._WaitOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_coroutine", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._WaitOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._WaitOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _WaitOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._WaitOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._WaitOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "url_scheme", "interceptor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "url_scheme", "interceptor"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport", "builtins.str", {".class": "UnionType", "items": ["google.auth.aio.credentials.Credentials", {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.api_core.gapic_v1.client_info.ClientInfo", "builtins.str", {".class": "UnionType", "items": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_interceptor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._interceptor", "name": "_interceptor", "setter_type": null, "type": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestInterceptor"}}, "_operations_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._operations_client", "name": "_operations_client", "setter_type": null, "type": {".class": "UnionType", "items": ["google.api_core.operations_v1.operations_rest_client_async.AsyncOperationsRestClient", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_prep_wrapped_messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "client_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._prep_wrapped_messages", "name": "_prep_wrapped_messages", "type": null}}, "_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._session", "name": "_session", "setter_type": null, "type": "google.auth.aio.transport.sessions.AsyncAuthorizedSession"}}, "_wrap_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "func", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._wrap_method", "name": "_wrap_method", "type": null}}, "_wrap_with_kind": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport._wrap_with_kind", "name": "_wrap_with_kind", "setter_type": null, "type": "builtins.bool"}}, "cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.cancel_operation", "name": "cancel_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.cancel_operation", "name": "cancel_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cancel_operation of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.close", "name": "close", "type": null}}, "create_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.create_feature", "name": "create_feature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_feature of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.CreateFeatureRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.create_feature", "name": "create_feature", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_feature of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.CreateFeatureRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_feature_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.create_feature_group", "name": "create_feature_group", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_feature_group of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.CreateFeatureGroupRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.create_feature_group", "name": "create_feature_group", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_feature_group of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.CreateFeatureGroupRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.delete_feature", "name": "delete_feature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_feature of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.DeleteFeatureRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.delete_feature", "name": "delete_feature", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_feature of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.DeleteFeatureRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_feature_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.delete_feature_group", "name": "delete_feature_group", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_feature_group of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.DeleteFeatureGroupRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.delete_feature_group", "name": "delete_feature_group", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_feature_group of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.DeleteFeatureGroupRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.delete_operation", "name": "delete_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.delete_operation", "name": "delete_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_operation of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.get_feature", "name": "get_feature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_feature of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.GetFeatureRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.feature.Feature", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.get_feature", "name": "get_feature", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_feature of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.GetFeatureRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.feature.Feature", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_feature_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.get_feature_group", "name": "get_feature_group", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_feature_group of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.GetFeatureGroupRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.feature_group.FeatureGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.get_feature_group", "name": "get_feature_group", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_feature_group of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.GetFeatureGroupRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.feature_group.FeatureGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.get_iam_policy", "name": "get_iam_policy", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.get_iam_policy", "name": "get_iam_policy", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_iam_policy of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.get_location", "name": "get_location", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.get_location", "name": "get_location", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_location of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.get_operation", "name": "get_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.get_operation", "name": "get_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_operation of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.kind", "name": "kind", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kind of AsyncFeatureRegistryServiceRestTransport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.kind", "name": "kind", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kind of AsyncFeatureRegistryServiceRestTransport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_feature_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.list_feature_groups", "name": "list_feature_groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_feature_groups of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.ListFeatureGroupsRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.feature_registry_service.ListFeatureGroupsResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.list_feature_groups", "name": "list_feature_groups", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_feature_groups of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.ListFeatureGroupsRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.feature_registry_service.ListFeatureGroupsResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.list_features", "name": "list_features", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_features of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.ListFeaturesRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.featurestore_service.ListFeaturesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.list_features", "name": "list_features", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_features of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.ListFeaturesRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.cloud.aiplatform_v1beta1.types.featurestore_service.ListFeaturesResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.list_locations", "name": "list_locations", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.list_locations", "name": "list_locations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_locations of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.list_operations", "name": "list_operations", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.list_operations", "name": "list_operations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_operations of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "operations_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.operations_client", "name": "operations_client", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "operations_client of AsyncFeatureRegistryServiceRestTransport", "ret_type": "google.api_core.operations_v1.operations_rest_client_async.AsyncOperationsRestClient", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.operations_client", "name": "operations_client", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "operations_client of AsyncFeatureRegistryServiceRestTransport", "ret_type": "google.api_core.operations_v1.operations_rest_client_async.AsyncOperationsRestClient", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_iam_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.set_iam_policy", "name": "set_iam_policy", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.set_iam_policy", "name": "set_iam_policy", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_iam_policy of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_iam_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.test_iam_permissions", "name": "test_iam_permissions", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.test_iam_permissions", "name": "test_iam_permissions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "test_iam_permissions of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.update_feature", "name": "update_feature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_feature of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.UpdateFeatureRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.update_feature", "name": "update_feature", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_feature of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.featurestore_service.UpdateFeatureRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update_feature_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.update_feature_group", "name": "update_feature_group", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_feature_group of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.UpdateFeatureGroupRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.update_feature_group", "name": "update_feature_group", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_feature_group of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.aiplatform_v1beta1.types.feature_registry_service.UpdateFeatureGroupRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "wait_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.wait_operation", "name": "wait_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.wait_operation", "name": "wait_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "wait_operation of AsyncFeatureRegistryServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.AsyncFeatureRegistryServiceRestTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncOperationsRestClient": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1.operations_rest_client_async.AsyncOperationsRestClient", "kind": "Gdef"}, "BASE_DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.base.DEFAULT_CLIENT_INFO", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.DEFAULT_CLIENT_INFO", "name": "DEFAULT_CLIENT_INFO", "setter_type": null, "type": "google.api_core.gapic_v1.client_info.ClientInfo"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OptionalRetry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.OptionalRetry", "line": 65, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.api_core.retry.retry_unary_async.AsyncRetry", "google.api_core.gapic_v1.method._MethodDefault", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_BaseFeatureRegistryServiceRestTransport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "aiohttp": {".class": "SymbolTableNode", "cross_ref": "aiohttp", "kind": "Gdef"}, "core_exceptions": {".class": "SymbolTableNode", "cross_ref": "google.api_core.exceptions", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.e", "name": "e", "setter_type": null, "type": {".class": "DeletedType", "source": "e"}}}, "feature": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature", "kind": "Gdef"}, "feature_group": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_group", "kind": "Gdef"}, "feature_registry_service": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_registry_service", "kind": "Gdef"}, "featurestore_service": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service", "kind": "Gdef"}, "ga_credentials_async": {".class": "SymbolTableNode", "cross_ref": "google.auth.aio.credentials", "kind": "Gdef"}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef"}, "google": {".class": "SymbolTableNode", "cross_ref": "google", "kind": "Gdef"}, "iam_policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.iam_policy_pb2", "name": "iam_policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.iam_policy_pb2", "source_any": null, "type_of_any": 3}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "json_format": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.json_format", "name": "json_format", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.json_format", "source_any": null, "type_of_any": 3}}}, "locations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.locations_pb2", "name": "locations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.locations_pb2", "source_any": null, "type_of_any": 3}}}, "operations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "name": "operations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.operations_pb2", "source_any": null, "type_of_any": 3}}}, "operations_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1", "kind": "Gdef"}, "policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.policy_pb2", "name": "policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_asyncio.policy_pb2", "source_any": null, "type_of_any": 3}}}, "rest_helpers": {".class": "SymbolTableNode", "cross_ref": "google.api_core.rest_helpers", "kind": "Gdef"}, "rest_streaming_async": {".class": "SymbolTableNode", "cross_ref": "google.api_core.rest_streaming_async", "kind": "Gdef"}, "retries": {".class": "SymbolTableNode", "cross_ref": "google.api_core.retry_async", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/feature_registry_service/transports/rest_asyncio.py"}