{"data_mtime": 1755541229, "dep_lines": [57, 61, 62, 63, 66, 51, 34, 41, 42, 51, 34, 36, 37, 38, 39, 40, 41, 43, 44, 726, 36, 40, 44, 16, 17, 18, 19, 32, 726, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 52, 53, 55, 56], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 5, 20, 20, 10, 10, 10, 10, 10, 20, 5, 10, 20, 20, 20, 20, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5], "dependencies": ["google.cloud.aiplatform_v1beta1.services.reasoning_engine_execution_service.transports.base", "google.cloud.aiplatform_v1beta1.services.reasoning_engine_execution_service.transports.grpc", "google.cloud.aiplatform_v1beta1.services.reasoning_engine_execution_service.transports.grpc_asyncio", "google.cloud.aiplatform_v1beta1.services.reasoning_engine_execution_service.transports.rest", "google.cloud.aiplatform_v1beta1.services.reasoning_engine_execution_service.transports.rest_asyncio", "google.cloud.aiplatform_v1beta1.types.reasoning_engine_execution_service", "google.cloud.aiplatform_v1beta1.gapic_version", "google.auth.transport.mtls", "google.auth.transport.grpc", "google.cloud.aiplatform_v1beta1.types", "google.cloud.aiplatform_v1beta1", "google.api_core.client_options", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.retry", "google.auth.credentials", "google.auth.transport", "google.auth.exceptions", "google.oauth2.service_account", "google.auth._default", "google.api_core", "google.auth", "google.oauth2", "collections", "os", "re", "typing", "warnings", "google", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.auth._credentials_base", "google.auth.aio", "google.auth.aio.credentials", "google.cloud.aiplatform_v1beta1.services.reasoning_engine_execution_service.transports", "google.cloud.aiplatform_v1beta1.services.reasoning_engine_execution_service.transports.rest_base", "types", "typing_extensions"], "hash": "5d6c1f5d77d6263188e783754d13c3aff75f023a", "id": "google.cloud.aiplatform_v1beta1.services.reasoning_engine_execution_service.client", "ignore_all": true, "interface_hash": "88f25e282f5aff2d26b6296d8fea799c3748e931", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/reasoning_engine_execution_service/client.py", "plugin_data": null, "size": 61840, "suppressed": ["google.cloud.location", "google.iam.v1", "google.longrunning", "google.protobuf"], "version_id": "1.17.1"}