{"data_mtime": 1755541229, "dep_lines": [35, 27, 29, 23, 27, 19, 20, 22, 19, 21, 16, 17, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 30, 31, 33, 34, 25], "dep_prios": [5, 10, 10, 5, 20, 10, 10, 10, 20, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5, 10], "dependencies": ["google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.base", "google.cloud.aiplatform_v1beta1.types.cached_content", "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service", "google.auth.transport.grpc", "google.cloud.aiplatform_v1beta1.types", "google.api_core.grpc_helpers", "google.api_core.gapic_v1", "google.auth.credentials", "google.api_core", "google.auth", "warnings", "typing", "google", "builtins", "_frozen_importlib", "_warnings", "abc", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.auth._credentials_base", "google.auth.transport", "types"], "hash": "e81dd3c4ae6dd9b388051eba4617ff385450af31", "id": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.transports.grpc", "ignore_all": true, "interface_hash": "de1ecfc14d4a1bc3e8068b6b76f076bfd5b485a1", "mtime": 1753678821, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/gen_ai_cache_service/transports/grpc.py", "plugin_data": null, "size": 27378, "suppressed": ["google.cloud.location", "google.iam.v1", "google.longrunning", "google.protobuf", "grpc"], "version_id": "1.17.1"}