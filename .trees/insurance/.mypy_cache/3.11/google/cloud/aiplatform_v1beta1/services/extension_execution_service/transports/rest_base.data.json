{".class": "MypyFile", "_fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.base.DEFAULT_CLIENT_INFO", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "ExtensionExecutionServiceTransport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.base.ExtensionExecutionServiceTransport", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_BaseExtensionExecutionServiceRestTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.base.ExtensionExecutionServiceTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport", "name": "_BaseExtensionExecutionServiceRestTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport", "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.base.ExtensionExecutionServiceTransport", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_BaseCancelOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseCancelOperation", "name": "_BaseCancelOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseCancelOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseCancelOperation", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseCancelOperation.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseCancelOperation._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseCancelOperation._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseCancelOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseCancelOperation._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseCancelOperation._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseCancelOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseCancelOperation._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseCancelOperation._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseCancelOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseCancelOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseCancelOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseDeleteOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseDeleteOperation", "name": "_BaseDeleteOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseDeleteOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseDeleteOperation", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseDeleteOperation.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseDeleteOperation._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseDeleteOperation._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseDeleteOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseDeleteOperation._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseDeleteOperation._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseDeleteOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseDeleteOperation._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseDeleteOperation._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseDeleteOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseDeleteOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseDeleteOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseExecuteExtension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension", "name": "_BaseExecuteExtension", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseExecuteExtension", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseExecuteExtension", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseExecuteExtension", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseExecuteExtension", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseExecuteExtension", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseExecuteExtension", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseGetIamPolicy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetIamPolicy", "name": "_BaseGetIamPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetIamPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetIamPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetIamPolicy.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetIamPolicy._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetIamPolicy._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseGetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetIamPolicy._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetIamPolicy._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseGetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetIamPolicy._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetIamPolicy._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseGetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetIamPolicy._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetIamPolicy._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseGetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetIamPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetIamPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseGetLocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetLocation", "name": "_BaseGetLocation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetLocation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetLocation", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetLocation.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetLocation._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetLocation._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseGetLocation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetLocation._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetLocation._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseGetLocation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetLocation._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetLocation._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseGetLocation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetLocation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetLocation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseGetOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetOperation", "name": "_BaseGetOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetOperation", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetOperation.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetOperation._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetOperation._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseGetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetOperation._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetOperation._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseGetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetOperation._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetOperation._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseGetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseGetOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseListLocations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListLocations", "name": "_BaseListLocations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListLocations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListLocations", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListLocations.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListLocations._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListLocations._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseListLocations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListLocations._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListLocations._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseListLocations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListLocations._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListLocations._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseListLocations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListLocations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListLocations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseListOperations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListOperations", "name": "_BaseListOperations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListOperations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListOperations", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListOperations.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListOperations._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListOperations._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseListOperations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListOperations._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListOperations._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseListOperations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListOperations._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListOperations._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseListOperations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListOperations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseListOperations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseQueryExtension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension", "name": "_BaseQueryExtension", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseQueryExtension", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseQueryExtension", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseQueryExtension", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseQueryExtension", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseQueryExtension", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseQueryExtension", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseSetIamPolicy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseSetIamPolicy", "name": "_BaseSetIamPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseSetIamPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseSetIamPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseSetIamPolicy.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseSetIamPolicy._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseSetIamPolicy._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseSetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseSetIamPolicy._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseSetIamPolicy._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseSetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseSetIamPolicy._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseSetIamPolicy._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseSetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseSetIamPolicy._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseSetIamPolicy._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseSetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseSetIamPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseSetIamPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseTestIamPermissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseTestIamPermissions", "name": "_BaseTestIamPermissions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseTestIamPermissions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseTestIamPermissions", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseTestIamPermissions.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseTestIamPermissions._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseTestIamPermissions._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseTestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseTestIamPermissions._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseTestIamPermissions._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseTestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseTestIamPermissions._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseTestIamPermissions._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseTestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseTestIamPermissions._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseTestIamPermissions._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseTestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseTestIamPermissions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseTestIamPermissions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseWaitOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseWaitOperation", "name": "_BaseWaitOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseWaitOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseWaitOperation", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseWaitOperation.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseWaitOperation._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseWaitOperation._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseWaitOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseWaitOperation._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseWaitOperation._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseWaitOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseWaitOperation._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseWaitOperation._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseWaitOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseWaitOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport._BaseWaitOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "always_use_jwt_access", "url_scheme", "api_audience"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "always_use_jwt_access", "url_scheme", "api_audience"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport", "builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.api_core.gapic_v1.client_info.ClientInfo", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _BaseExtensionExecutionServiceRestTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base._BaseExtensionExecutionServiceRestTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "extension_execution_service": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension_execution_service", "kind": "Gdef", "module_public": false}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef", "module_public": false}, "iam_policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.iam_policy_pb2", "name": "iam_policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.iam_policy_pb2", "source_any": null, "type_of_any": 3}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "json_format": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.json_format", "name": "json_format", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.json_format", "source_any": null, "type_of_any": 3}}}, "locations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.locations_pb2", "name": "locations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.locations_pb2", "source_any": null, "type_of_any": 3}}}, "operations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.operations_pb2", "name": "operations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.operations_pb2", "source_any": null, "type_of_any": 3}}}, "path_template": {".class": "SymbolTableNode", "cross_ref": "google.api_core.path_template", "kind": "Gdef", "module_public": false}, "policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.policy_pb2", "name": "policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.transports.rest_base.policy_pb2", "source_any": null, "type_of_any": 3}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/extension_execution_service/transports/rest_base.py"}