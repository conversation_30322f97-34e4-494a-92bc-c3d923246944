{"data_mtime": 1755541229, "dep_lines": [55, 57, 21, 51, 29, 51, 22, 23, 29, 31, 32, 36, 37, 17, 22, 17, 20, 46, 47, 48, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 33, 35, 41, 52], "dep_prios": [5, 5, 5, 10, 10, 20, 10, 5, 20, 10, 10, 10, 10, 10, 20, 20, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5], "dependencies": ["google.cloud.aiplatform_v1beta1.services.match_service.transports.rest_base", "google.cloud.aiplatform_v1beta1.services.match_service.transports.base", "google.auth.aio.transport.sessions", "google.cloud.aiplatform_v1beta1.types.match_service", "google.auth.aio.credentials", "google.cloud.aiplatform_v1beta1.types", "google.api_core.rest_streaming_async", "google.api_core.operations_v1", "google.auth.aio", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.retry_async", "google.api_core.rest_helpers", "google.auth", "google.api_core", "google", "aiohttp", "json", "dataclasses", "typing", "builtins", "_frozen_importlib", "abc", "enum", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.retry", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary_async", "google.auth._credentials_base", "google.auth.aio.transport", "google.auth.credentials"], "hash": "052884fd492b10af63e0170c83add3a4b5a3163f", "id": "google.cloud.aiplatform_v1beta1.services.match_service.transports.rest_asyncio", "ignore_all": true, "interface_hash": "d064007118d47a8d8f00dfdb50fe783ccd7f16d5", "mtime": 1753678821, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/match_service/transports/rest_asyncio.py", "plugin_data": null, "size": 67608, "suppressed": ["google.iam.v1", "google.cloud.location", "google.protobuf", "google.longrunning"], "version_id": "1.17.1"}