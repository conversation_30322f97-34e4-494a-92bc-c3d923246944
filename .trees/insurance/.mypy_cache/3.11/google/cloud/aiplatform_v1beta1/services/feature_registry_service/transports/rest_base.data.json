{".class": "MypyFile", "_fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.base.DEFAULT_CLIENT_INFO", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "FeatureRegistryServiceTransport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.base.FeatureRegistryServiceTransport", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_BaseFeatureRegistryServiceRestTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.base.FeatureRegistryServiceTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport", "name": "_BaseFeatureRegistryServiceRestTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport", "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.base.FeatureRegistryServiceTransport", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_BaseCancelOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCancelOperation", "name": "_BaseCancelOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCancelOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCancelOperation", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCancelOperation.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCancelOperation._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCancelOperation._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseCancelOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCancelOperation._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCancelOperation._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseCancelOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCancelOperation._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCancelOperation._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseCancelOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCancelOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCancelOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseCreateFeature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature", "name": "_BaseCreateFeature", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseCreateFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseCreateFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseCreateFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseCreateFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseCreateFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeature", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseCreateFeatureGroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup", "name": "_BaseCreateFeatureGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseCreateFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseCreateFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseCreateFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseCreateFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseCreateFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseCreateFeatureGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseDeleteFeature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature", "name": "_BaseDeleteFeature", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseDeleteFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseDeleteFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseDeleteFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseDeleteFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeature", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseDeleteFeatureGroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup", "name": "_BaseDeleteFeatureGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseDeleteFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseDeleteFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseDeleteFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseDeleteFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteFeatureGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseDeleteOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteOperation", "name": "_BaseDeleteOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteOperation", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteOperation.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteOperation._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteOperation._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseDeleteOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteOperation._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteOperation._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseDeleteOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteOperation._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteOperation._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseDeleteOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseDeleteOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseGetFeature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature", "name": "_BaseGetFeature", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseGetFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseGetFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseGetFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseGetFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeature", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseGetFeatureGroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup", "name": "_BaseGetFeatureGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseGetFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseGetFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseGetFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseGetFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetFeatureGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseGetIamPolicy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy", "name": "_BaseGetIamPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseGetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseGetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseGetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseGetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetIamPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseGetLocation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetLocation", "name": "_BaseGetLocation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetLocation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetLocation", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetLocation.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetLocation._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetLocation._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseGetLocation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetLocation._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetLocation._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseGetLocation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetLocation._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetLocation._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseGetLocation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetLocation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetLocation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseGetOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetOperation", "name": "_BaseGetOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetOperation", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetOperation.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetOperation._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetOperation._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseGetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetOperation._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetOperation._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseGetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetOperation._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetOperation._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseGetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseGetOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseListFeatureGroups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups", "name": "_BaseListFeatureGroups", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseListFeatureGroups", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseListFeatureGroups", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseListFeatureGroups", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseListFeatureGroups", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatureGroups", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseListFeatures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures", "name": "_BaseListFeatures", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseListFeatures", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseListFeatures", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseListFeatures", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseListFeatures", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListFeatures", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseListLocations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListLocations", "name": "_BaseListLocations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListLocations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListLocations", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListLocations.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListLocations._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListLocations._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseListLocations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListLocations._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListLocations._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseListLocations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListLocations._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListLocations._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseListLocations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListLocations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListLocations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseListOperations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListOperations", "name": "_BaseListOperations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListOperations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListOperations", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListOperations.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListOperations._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListOperations._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseListOperations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListOperations._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListOperations._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseListOperations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListOperations._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListOperations._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseListOperations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListOperations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseListOperations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseSetIamPolicy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy", "name": "_BaseSetIamPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseSetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseSetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseSetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseSetIamPolicy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseSetIamPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseTestIamPermissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions", "name": "_BaseTestIamPermissions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseTestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseTestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseTestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseTestIamPermissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseTestIamPermissions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseUpdateFeature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature", "name": "_BaseUpdateFeature", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseUpdateFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseUpdateFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseUpdateFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseUpdateFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseUpdateFeature", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeature", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseUpdateFeatureGroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup", "name": "_BaseUpdateFeatureGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup", "builtins.object"], "names": {".class": "SymbolTable", "__REQUIRED_FIELDS_DEFAULT_VALUES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup.__REQUIRED_FIELDS_DEFAULT_VALUES", "name": "__REQUIRED_FIELDS_DEFAULT_VALUES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseUpdateFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseUpdateFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_request_body_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup._get_request_body_json", "name": "_get_request_body_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup._get_request_body_json", "name": "_get_request_body_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_request_body_json of _BaseUpdateFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseUpdateFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_unset_required_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup._get_unset_required_fields", "name": "_get_unset_required_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup._get_unset_required_fields", "name": "_get_unset_required_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message_dict"], "arg_types": [{".class": "TypeType", "item": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_unset_required_fields of _BaseUpdateFeatureGroup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseUpdateFeatureGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseWaitOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseWaitOperation", "name": "_BaseWaitOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseWaitOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base", "mro": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseWaitOperation", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseWaitOperation.__hash__", "name": "__hash__", "type": null}}, "_get_http_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseWaitOperation._get_http_options", "name": "_get_http_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseWaitOperation._get_http_options", "name": "_get_http_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_http_options of _BaseWaitOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_query_params_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["transcoded_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseWaitOperation._get_query_params_json", "name": "_get_query_params_json", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseWaitOperation._get_query_params_json", "name": "_get_query_params_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["transcoded_request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_query_params_json of _BaseWaitOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_transcoded_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseWaitOperation._get_transcoded_request", "name": "_get_transcoded_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseWaitOperation._get_transcoded_request", "name": "_get_transcoded_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["http_options", "request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_transcoded_request of _BaseWaitOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseWaitOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport._BaseWaitOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "always_use_jwt_access", "url_scheme", "api_audience"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "client_info", "always_use_jwt_access", "url_scheme", "api_audience"], "arg_types": ["google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport", "builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.api_core.gapic_v1.client_info.ClientInfo", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _BaseFeatureRegistryServiceRestTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base._BaseFeatureRegistryServiceRestTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "feature": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature", "kind": "Gdef", "module_public": false}, "feature_group": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_group", "kind": "Gdef", "module_public": false}, "feature_registry_service": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_registry_service", "kind": "Gdef", "module_public": false}, "featurestore_service": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service", "kind": "Gdef", "module_public": false}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef", "module_public": false}, "iam_policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.iam_policy_pb2", "name": "iam_policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.iam_policy_pb2", "source_any": null, "type_of_any": 3}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "json_format": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.json_format", "name": "json_format", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.json_format", "source_any": null, "type_of_any": 3}}}, "locations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.locations_pb2", "name": "locations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.locations_pb2", "source_any": null, "type_of_any": 3}}}, "operations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.operations_pb2", "name": "operations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.operations_pb2", "source_any": null, "type_of_any": 3}}}, "path_template": {".class": "SymbolTableNode", "cross_ref": "google.api_core.path_template", "kind": "Gdef", "module_public": false}, "policy_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.policy_pb2", "name": "policy_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.transports.rest_base.policy_pb2", "source_any": null, "type_of_any": 3}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/feature_registry_service/transports/rest_base.py"}