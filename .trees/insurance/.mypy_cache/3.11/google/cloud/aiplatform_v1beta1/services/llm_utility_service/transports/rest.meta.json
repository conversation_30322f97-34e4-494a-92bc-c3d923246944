{"data_mtime": 1755541229, "dep_lines": [41, 42, 37, 17, 37, 19, 20, 21, 22, 23, 24, 19, 20, 18, 32, 33, 34, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 27, 29, 26, 38, 31], "dep_prios": [5, 5, 10, 5, 20, 10, 10, 10, 10, 10, 10, 20, 20, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5], "dependencies": ["google.cloud.aiplatform_v1beta1.services.llm_utility_service.transports.rest_base", "google.cloud.aiplatform_v1beta1.services.llm_utility_service.transports.base", "google.cloud.aiplatform_v1beta1.types.llm_utility_service", "google.auth.transport.requests", "google.cloud.aiplatform_v1beta1.types", "google.auth.credentials", "google.api_core.exceptions", "google.api_core.retry", "google.api_core.rest_helpers", "google.api_core.rest_streaming", "google.api_core.gapic_v1", "google.auth", "google.api_core", "json", "dataclasses", "typing", "warnings", "builtins", "_frozen_importlib", "abc", "enum", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.auth._credentials_base", "google.auth.transport"], "hash": "4480d540e2de6d00630a71cf9dbd6863e540684e", "id": "google.cloud.aiplatform_v1beta1.services.llm_utility_service.transports.rest", "ignore_all": true, "interface_hash": "75af695e2fc53131908845830c99f547599194cf", "mtime": 1753678821, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/services/llm_utility_service/transports/rest.py", "plugin_data": null, "size": 54807, "suppressed": ["google.iam.v1", "google.cloud.location", "google.protobuf", "google.longrunning", "requests"], "version_id": "1.17.1"}