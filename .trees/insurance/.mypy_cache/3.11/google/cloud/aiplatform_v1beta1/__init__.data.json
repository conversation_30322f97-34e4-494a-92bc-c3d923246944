{".class": "MypyFile", "_fullname": "google.cloud.aiplatform_v1beta1", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AcceleratorType": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.accelerator_type.AcceleratorType", "kind": "Gdef"}, "ActiveLearningConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.data_labeling_job.ActiveLearningConfig", "kind": "Gdef"}, "AddContextArtifactsAndExecutionsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.AddContextArtifactsAndExecutionsRequest", "kind": "Gdef"}, "AddContextArtifactsAndExecutionsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.AddContextArtifactsAndExecutionsResponse", "kind": "Gdef"}, "AddContextChildrenRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.AddContextChildrenRequest", "kind": "Gdef"}, "AddContextChildrenResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.AddContextChildrenResponse", "kind": "Gdef"}, "AddExecutionEventsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.AddExecutionEventsRequest", "kind": "Gdef"}, "AddExecutionEventsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.AddExecutionEventsResponse", "kind": "Gdef"}, "AddTrialMeasurementRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.AddTrialMeasurementRequest", "kind": "Gdef"}, "Annotation": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.annotation.Annotation", "kind": "Gdef"}, "AnnotationSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.annotation_spec.AnnotationSpec", "kind": "Gdef"}, "ApiAuth": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.api_auth.ApiAuth", "kind": "Gdef"}, "Artifact": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.artifact.Artifact", "kind": "Gdef"}, "ArtifactTypeSchema": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.ui_pipeline_spec.ArtifactTypeSchema", "kind": "Gdef"}, "AssignNotebookRuntimeOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.AssignNotebookRuntimeOperationMetadata", "kind": "Gdef"}, "AssignNotebookRuntimeRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.AssignNotebookRuntimeRequest", "kind": "Gdef"}, "Attribution": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.Attribution", "kind": "Gdef"}, "AuthConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension.AuthConfig", "kind": "Gdef"}, "AuthType": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension.AuthType", "kind": "Gdef"}, "AutomaticResources": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.machine_resources.AutomaticResources", "kind": "Gdef"}, "AutoscalingMetricSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.machine_resources.AutoscalingMetricSpec", "kind": "Gdef"}, "AvroSource": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.io.AvroSource", "kind": "Gdef"}, "BatchCancelPipelineJobsOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.BatchCancelPipelineJobsOperationMetadata", "kind": "Gdef"}, "BatchCancelPipelineJobsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.BatchCancelPipelineJobsRequest", "kind": "Gdef"}, "BatchCancelPipelineJobsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.BatchCancelPipelineJobsResponse", "kind": "Gdef"}, "BatchCreateFeaturesOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.BatchCreateFeaturesOperationMetadata", "kind": "Gdef"}, "BatchCreateFeaturesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.BatchCreateFeaturesRequest", "kind": "Gdef"}, "BatchCreateFeaturesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.BatchCreateFeaturesResponse", "kind": "Gdef"}, "BatchCreateTensorboardRunsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.BatchCreateTensorboardRunsRequest", "kind": "Gdef"}, "BatchCreateTensorboardRunsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.BatchCreateTensorboardRunsResponse", "kind": "Gdef"}, "BatchCreateTensorboardTimeSeriesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.BatchCreateTensorboardTimeSeriesRequest", "kind": "Gdef"}, "BatchCreateTensorboardTimeSeriesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.BatchCreateTensorboardTimeSeriesResponse", "kind": "Gdef"}, "BatchDedicatedResources": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.machine_resources.BatchDedicatedResources", "kind": "Gdef"}, "BatchDeletePipelineJobsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.BatchDeletePipelineJobsRequest", "kind": "Gdef"}, "BatchDeletePipelineJobsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.BatchDeletePipelineJobsResponse", "kind": "Gdef"}, "BatchImportEvaluatedAnnotationsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.BatchImportEvaluatedAnnotationsRequest", "kind": "Gdef"}, "BatchImportEvaluatedAnnotationsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.BatchImportEvaluatedAnnotationsResponse", "kind": "Gdef"}, "BatchImportModelEvaluationSlicesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.BatchImportModelEvaluationSlicesRequest", "kind": "Gdef"}, "BatchImportModelEvaluationSlicesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.BatchImportModelEvaluationSlicesResponse", "kind": "Gdef"}, "BatchMigrateResourcesOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.migration_service.BatchMigrateResourcesOperationMetadata", "kind": "Gdef"}, "BatchMigrateResourcesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.migration_service.BatchMigrateResourcesRequest", "kind": "Gdef"}, "BatchMigrateResourcesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.migration_service.BatchMigrateResourcesResponse", "kind": "Gdef"}, "BatchPredictionJob": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.batch_prediction_job.BatchPredictionJob", "kind": "Gdef"}, "BatchReadFeatureValuesOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.BatchReadFeatureValuesOperationMetadata", "kind": "Gdef"}, "BatchReadFeatureValuesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.BatchReadFeatureValuesRequest", "kind": "Gdef"}, "BatchReadFeatureValuesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.BatchReadFeatureValuesResponse", "kind": "Gdef"}, "BatchReadTensorboardTimeSeriesDataRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.BatchReadTensorboardTimeSeriesDataRequest", "kind": "Gdef"}, "BatchReadTensorboardTimeSeriesDataResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.BatchReadTensorboardTimeSeriesDataResponse", "kind": "Gdef"}, "BigQueryDestination": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.io.BigQueryDestination", "kind": "Gdef"}, "BigQuerySource": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.io.BigQuerySource", "kind": "Gdef"}, "BleuInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.BleuInput", "kind": "Gdef"}, "BleuInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.BleuInstance", "kind": "Gdef"}, "BleuMetricValue": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.BleuMetricValue", "kind": "Gdef"}, "BleuResults": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.BleuResults", "kind": "Gdef"}, "BleuSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.BleuSpec", "kind": "Gdef"}, "Blob": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.Blob", "kind": "Gdef"}, "BlurBaselineConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.BlurBaselineConfig", "kind": "Gdef"}, "BoolArray": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.types.BoolArray", "kind": "Gdef"}, "CachedContent": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.cached_content.CachedContent", "kind": "Gdef"}, "CancelBatchPredictionJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.CancelBatchPredictionJobRequest", "kind": "Gdef"}, "CancelCustomJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.CancelCustomJobRequest", "kind": "Gdef"}, "CancelDataLabelingJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.CancelDataLabelingJobRequest", "kind": "Gdef"}, "CancelHyperparameterTuningJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.CancelHyperparameterTuningJobRequest", "kind": "Gdef"}, "CancelNasJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.CancelNasJobRequest", "kind": "Gdef"}, "CancelPipelineJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.CancelPipelineJobRequest", "kind": "Gdef"}, "CancelTrainingPipelineRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.CancelTrainingPipelineRequest", "kind": "Gdef"}, "CancelTuningJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.genai_tuning_service.CancelTuningJobRequest", "kind": "Gdef"}, "Candidate": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.Candidate", "kind": "Gdef"}, "ChatCompletionsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.ChatCompletionsRequest", "kind": "Gdef"}, "CheckTrialEarlyStoppingStateMetatdata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.CheckTrialEarlyStoppingStateMetatdata", "kind": "Gdef"}, "CheckTrialEarlyStoppingStateRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.CheckTrialEarlyStoppingStateRequest", "kind": "Gdef"}, "CheckTrialEarlyStoppingStateResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.CheckTrialEarlyStoppingStateResponse", "kind": "Gdef"}, "Citation": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.Citation", "kind": "Gdef"}, "CitationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.CitationMetadata", "kind": "Gdef"}, "CoherenceInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.CoherenceInput", "kind": "Gdef"}, "CoherenceInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.CoherenceInstance", "kind": "Gdef"}, "CoherenceResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.CoherenceResult", "kind": "Gdef"}, "CoherenceSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.CoherenceSpec", "kind": "Gdef"}, "CompleteTrialRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.CompleteTrialRequest", "kind": "Gdef"}, "CompletionStats": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.completion_stats.CompletionStats", "kind": "Gdef"}, "ComputeTokensRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.llm_utility_service.ComputeTokensRequest", "kind": "Gdef"}, "ComputeTokensResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.llm_utility_service.ComputeTokensResponse", "kind": "Gdef"}, "ContainerRegistryDestination": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.io.ContainerRegistryDestination", "kind": "Gdef"}, "ContainerSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.custom_job.ContainerSpec", "kind": "Gdef"}, "Content": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.Content", "kind": "Gdef"}, "Context": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.context.Context", "kind": "Gdef"}, "CopyModelOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.CopyModelOperationMetadata", "kind": "Gdef"}, "CopyModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.CopyModelRequest", "kind": "Gdef"}, "CopyModelResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.CopyModelResponse", "kind": "Gdef"}, "CorpusStatus": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data.CorpusStatus", "kind": "Gdef"}, "CountTokensRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.CountTokensRequest", "kind": "Gdef"}, "CountTokensResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.CountTokensResponse", "kind": "Gdef"}, "CreateArtifactRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.CreateArtifactRequest", "kind": "Gdef"}, "CreateBatchPredictionJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.CreateBatchPredictionJobRequest", "kind": "Gdef"}, "CreateCachedContentRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.CreateCachedContentRequest", "kind": "Gdef"}, "CreateContextRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.CreateContextRequest", "kind": "Gdef"}, "CreateCustomJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.CreateCustomJobRequest", "kind": "Gdef"}, "CreateDataLabelingJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.CreateDataLabelingJobRequest", "kind": "Gdef"}, "CreateDatasetOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.CreateDatasetOperationMetadata", "kind": "Gdef"}, "CreateDatasetRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.CreateDatasetRequest", "kind": "Gdef"}, "CreateDatasetVersionOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.CreateDatasetVersionOperationMetadata", "kind": "Gdef"}, "CreateDatasetVersionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.CreateDatasetVersionRequest", "kind": "Gdef"}, "CreateDeploymentResourcePoolOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.deployment_resource_pool_service.CreateDeploymentResourcePoolOperationMetadata", "kind": "Gdef"}, "CreateDeploymentResourcePoolRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.deployment_resource_pool_service.CreateDeploymentResourcePoolRequest", "kind": "Gdef"}, "CreateEndpointOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.CreateEndpointOperationMetadata", "kind": "Gdef"}, "CreateEndpointRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.CreateEndpointRequest", "kind": "Gdef"}, "CreateEntityTypeOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.CreateEntityTypeOperationMetadata", "kind": "Gdef"}, "CreateEntityTypeRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.CreateEntityTypeRequest", "kind": "Gdef"}, "CreateExecutionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.CreateExecutionRequest", "kind": "Gdef"}, "CreateFeatureGroupOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_registry_service.CreateFeatureGroupOperationMetadata", "kind": "Gdef"}, "CreateFeatureGroupRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_registry_service.CreateFeatureGroupRequest", "kind": "Gdef"}, "CreateFeatureOnlineStoreOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.CreateFeatureOnlineStoreOperationMetadata", "kind": "Gdef"}, "CreateFeatureOnlineStoreRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.CreateFeatureOnlineStoreRequest", "kind": "Gdef"}, "CreateFeatureOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.CreateFeatureOperationMetadata", "kind": "Gdef"}, "CreateFeatureRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.CreateFeatureRequest", "kind": "Gdef"}, "CreateFeatureViewOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.CreateFeatureViewOperationMetadata", "kind": "Gdef"}, "CreateFeatureViewRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.CreateFeatureViewRequest", "kind": "Gdef"}, "CreateFeaturestoreOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.CreateFeaturestoreOperationMetadata", "kind": "Gdef"}, "CreateFeaturestoreRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.CreateFeaturestoreRequest", "kind": "Gdef"}, "CreateHyperparameterTuningJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.CreateHyperparameterTuningJobRequest", "kind": "Gdef"}, "CreateIndexEndpointOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.CreateIndexEndpointOperationMetadata", "kind": "Gdef"}, "CreateIndexEndpointRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.CreateIndexEndpointRequest", "kind": "Gdef"}, "CreateIndexOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_service.CreateIndexOperationMetadata", "kind": "Gdef"}, "CreateIndexRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_service.CreateIndexRequest", "kind": "Gdef"}, "CreateMetadataSchemaRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.CreateMetadataSchemaRequest", "kind": "Gdef"}, "CreateMetadataStoreOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.CreateMetadataStoreOperationMetadata", "kind": "Gdef"}, "CreateMetadataStoreRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.CreateMetadataStoreRequest", "kind": "Gdef"}, "CreateModelDeploymentMonitoringJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.CreateModelDeploymentMonitoringJobRequest", "kind": "Gdef"}, "CreateModelMonitorOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.CreateModelMonitorOperationMetadata", "kind": "Gdef"}, "CreateModelMonitorRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.CreateModelMonitorRequest", "kind": "Gdef"}, "CreateModelMonitoringJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.CreateModelMonitoringJobRequest", "kind": "Gdef"}, "CreateNasJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.CreateNasJobRequest", "kind": "Gdef"}, "CreateNotebookExecutionJobOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.CreateNotebookExecutionJobOperationMetadata", "kind": "Gdef"}, "CreateNotebookExecutionJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.CreateNotebookExecutionJobRequest", "kind": "Gdef"}, "CreateNotebookRuntimeTemplateOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.CreateNotebookRuntimeTemplateOperationMetadata", "kind": "Gdef"}, "CreateNotebookRuntimeTemplateRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.CreateNotebookRuntimeTemplateRequest", "kind": "Gdef"}, "CreatePersistentResourceOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource_service.CreatePersistentResourceOperationMetadata", "kind": "Gdef"}, "CreatePersistentResourceRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource_service.CreatePersistentResourceRequest", "kind": "Gdef"}, "CreatePipelineJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.CreatePipelineJobRequest", "kind": "Gdef"}, "CreateRagCorpusOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.CreateRagCorpusOperationMetadata", "kind": "Gdef"}, "CreateRagCorpusRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.CreateRagCorpusRequest", "kind": "Gdef"}, "CreateReasoningEngineOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.reasoning_engine_service.CreateReasoningEngineOperationMetadata", "kind": "Gdef"}, "CreateReasoningEngineRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.reasoning_engine_service.CreateReasoningEngineRequest", "kind": "Gdef"}, "CreateRegistryFeatureOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_registry_service.CreateRegistryFeatureOperationMetadata", "kind": "Gdef"}, "CreateScheduleRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.schedule_service.CreateScheduleRequest", "kind": "Gdef"}, "CreateSpecialistPoolOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.specialist_pool_service.CreateSpecialistPoolOperationMetadata", "kind": "Gdef"}, "CreateSpecialistPoolRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.specialist_pool_service.CreateSpecialistPoolRequest", "kind": "Gdef"}, "CreateStudyRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.CreateStudyRequest", "kind": "Gdef"}, "CreateTensorboardExperimentRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.CreateTensorboardExperimentRequest", "kind": "Gdef"}, "CreateTensorboardOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.CreateTensorboardOperationMetadata", "kind": "Gdef"}, "CreateTensorboardRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.CreateTensorboardRequest", "kind": "Gdef"}, "CreateTensorboardRunRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.CreateTensorboardRunRequest", "kind": "Gdef"}, "CreateTensorboardTimeSeriesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.CreateTensorboardTimeSeriesRequest", "kind": "Gdef"}, "CreateTrainingPipelineRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.CreateTrainingPipelineRequest", "kind": "Gdef"}, "CreateTrialRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.CreateTrialRequest", "kind": "Gdef"}, "CreateTuningJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.genai_tuning_service.CreateTuningJobRequest", "kind": "Gdef"}, "CsvDestination": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.io.CsvDestination", "kind": "Gdef"}, "CsvSource": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.io.CsvSource", "kind": "Gdef"}, "CustomJob": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.custom_job.CustomJob", "kind": "Gdef"}, "CustomJobSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.custom_job.CustomJobSpec", "kind": "Gdef"}, "DataItem": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.data_item.DataItem", "kind": "Gdef"}, "DataItemView": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.DataItemView", "kind": "Gdef"}, "DataLabelingJob": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.data_labeling_job.DataLabelingJob", "kind": "Gdef"}, "Dataset": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset.Dataset", "kind": "Gdef"}, "DatasetDistribution": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tuning_job.DatasetDistribution", "kind": "Gdef"}, "DatasetServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.dataset_service.async_client.DatasetServiceAsyncClient", "kind": "Gdef"}, "DatasetServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.dataset_service.client.DatasetServiceClient", "kind": "Gdef"}, "DatasetStats": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tuning_job.DatasetStats", "kind": "Gdef"}, "DatasetVersion": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_version.DatasetVersion", "kind": "Gdef"}, "DedicatedResources": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.machine_resources.DedicatedResources", "kind": "Gdef"}, "DeleteArtifactRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.DeleteArtifactRequest", "kind": "Gdef"}, "DeleteBatchPredictionJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.DeleteBatchPredictionJobRequest", "kind": "Gdef"}, "DeleteCachedContentRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.DeleteCachedContentRequest", "kind": "Gdef"}, "DeleteContextRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.DeleteContextRequest", "kind": "Gdef"}, "DeleteCustomJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.DeleteCustomJobRequest", "kind": "Gdef"}, "DeleteDataLabelingJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.DeleteDataLabelingJobRequest", "kind": "Gdef"}, "DeleteDatasetRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.DeleteDatasetRequest", "kind": "Gdef"}, "DeleteDatasetVersionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.DeleteDatasetVersionRequest", "kind": "Gdef"}, "DeleteDeploymentResourcePoolRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.deployment_resource_pool_service.DeleteDeploymentResourcePoolRequest", "kind": "Gdef"}, "DeleteEndpointRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.DeleteEndpointRequest", "kind": "Gdef"}, "DeleteEntityTypeRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.DeleteEntityTypeRequest", "kind": "Gdef"}, "DeleteExecutionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.DeleteExecutionRequest", "kind": "Gdef"}, "DeleteExtensionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension_registry_service.DeleteExtensionRequest", "kind": "Gdef"}, "DeleteFeatureGroupRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_registry_service.DeleteFeatureGroupRequest", "kind": "Gdef"}, "DeleteFeatureOnlineStoreRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.DeleteFeatureOnlineStoreRequest", "kind": "Gdef"}, "DeleteFeatureRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.DeleteFeatureRequest", "kind": "Gdef"}, "DeleteFeatureValuesOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.DeleteFeatureValuesOperationMetadata", "kind": "Gdef"}, "DeleteFeatureValuesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.DeleteFeatureValuesRequest", "kind": "Gdef"}, "DeleteFeatureValuesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.DeleteFeatureValuesResponse", "kind": "Gdef"}, "DeleteFeatureViewRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.DeleteFeatureViewRequest", "kind": "Gdef"}, "DeleteFeaturestoreRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.DeleteFeaturestoreRequest", "kind": "Gdef"}, "DeleteHyperparameterTuningJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.DeleteHyperparameterTuningJobRequest", "kind": "Gdef"}, "DeleteIndexEndpointRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.DeleteIndexEndpointRequest", "kind": "Gdef"}, "DeleteIndexRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_service.DeleteIndexRequest", "kind": "Gdef"}, "DeleteMetadataStoreOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.DeleteMetadataStoreOperationMetadata", "kind": "Gdef"}, "DeleteMetadataStoreRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.DeleteMetadataStoreRequest", "kind": "Gdef"}, "DeleteModelDeploymentMonitoringJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.DeleteModelDeploymentMonitoringJobRequest", "kind": "Gdef"}, "DeleteModelMonitorRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.DeleteModelMonitorRequest", "kind": "Gdef"}, "DeleteModelMonitoringJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.DeleteModelMonitoringJobRequest", "kind": "Gdef"}, "DeleteModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.DeleteModelRequest", "kind": "Gdef"}, "DeleteModelVersionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.DeleteModelVersionRequest", "kind": "Gdef"}, "DeleteNasJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.DeleteNasJobRequest", "kind": "Gdef"}, "DeleteNotebookExecutionJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.DeleteNotebookExecutionJobRequest", "kind": "Gdef"}, "DeleteNotebookRuntimeRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.DeleteNotebookRuntimeRequest", "kind": "Gdef"}, "DeleteNotebookRuntimeTemplateRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.DeleteNotebookRuntimeTemplateRequest", "kind": "Gdef"}, "DeleteOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.operation.DeleteOperationMetadata", "kind": "Gdef"}, "DeletePersistentResourceRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource_service.DeletePersistentResourceRequest", "kind": "Gdef"}, "DeletePipelineJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.DeletePipelineJobRequest", "kind": "Gdef"}, "DeleteRagCorpusRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.DeleteRagCorpusRequest", "kind": "Gdef"}, "DeleteRagFileRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.DeleteRagFileRequest", "kind": "Gdef"}, "DeleteReasoningEngineRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.reasoning_engine_service.DeleteReasoningEngineRequest", "kind": "Gdef"}, "DeleteSavedQueryRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.DeleteSavedQueryRequest", "kind": "Gdef"}, "DeleteScheduleRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.schedule_service.DeleteScheduleRequest", "kind": "Gdef"}, "DeleteSpecialistPoolRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.specialist_pool_service.DeleteSpecialistPoolRequest", "kind": "Gdef"}, "DeleteStudyRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.DeleteStudyRequest", "kind": "Gdef"}, "DeleteTensorboardExperimentRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.DeleteTensorboardExperimentRequest", "kind": "Gdef"}, "DeleteTensorboardRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.DeleteTensorboardRequest", "kind": "Gdef"}, "DeleteTensorboardRunRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.DeleteTensorboardRunRequest", "kind": "Gdef"}, "DeleteTensorboardTimeSeriesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.DeleteTensorboardTimeSeriesRequest", "kind": "Gdef"}, "DeleteTrainingPipelineRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.DeleteTrainingPipelineRequest", "kind": "Gdef"}, "DeleteTrialRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.DeleteTrialRequest", "kind": "Gdef"}, "DeployIndexOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.DeployIndexOperationMetadata", "kind": "Gdef"}, "DeployIndexRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.DeployIndexRequest", "kind": "Gdef"}, "DeployIndexResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.DeployIndexResponse", "kind": "Gdef"}, "DeployModelOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.DeployModelOperationMetadata", "kind": "Gdef"}, "DeployModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.DeployModelRequest", "kind": "Gdef"}, "DeployModelResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.DeployModelResponse", "kind": "Gdef"}, "DeployedIndex": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint.DeployedIndex", "kind": "Gdef"}, "DeployedIndexAuthConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint.DeployedIndexAuthConfig", "kind": "Gdef"}, "DeployedIndexRef": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.deployed_index_ref.DeployedIndexRef", "kind": "Gdef"}, "DeployedModel": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint.DeployedModel", "kind": "Gdef"}, "DeployedModelRef": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.deployed_model_ref.DeployedModelRef", "kind": "Gdef"}, "DeploymentResourcePool": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.deployment_resource_pool.DeploymentResourcePool", "kind": "Gdef"}, "DeploymentResourcePoolServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.deployment_resource_pool_service.async_client.DeploymentResourcePoolServiceAsyncClient", "kind": "Gdef"}, "DeploymentResourcePoolServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.deployment_resource_pool_service.client.DeploymentResourcePoolServiceClient", "kind": "Gdef"}, "DestinationFeatureSetting": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.DestinationFeatureSetting", "kind": "Gdef"}, "DirectPredictRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.DirectPredictRequest", "kind": "Gdef"}, "DirectPredictResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.DirectPredictResponse", "kind": "Gdef"}, "DirectRawPredictRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.DirectRawPredictRequest", "kind": "Gdef"}, "DirectRawPredictResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.DirectRawPredictResponse", "kind": "Gdef"}, "DirectUploadSource": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.io.DirectUploadSource", "kind": "Gdef"}, "DiskSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.machine_resources.DiskSpec", "kind": "Gdef"}, "DistillationDataStats": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tuning_job.DistillationDataStats", "kind": "Gdef"}, "DistillationHyperParameters": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tuning_job.DistillationHyperParameters", "kind": "Gdef"}, "DistillationSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tuning_job.DistillationSpec", "kind": "Gdef"}, "DoubleArray": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.types.DoubleArray", "kind": "Gdef"}, "DynamicRetrievalConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tool.DynamicRetrievalConfig", "kind": "Gdef"}, "EncryptionSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.encryption_spec.EncryptionSpec", "kind": "Gdef"}, "Endpoint": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint.Endpoint", "kind": "Gdef"}, "EndpointServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.endpoint_service.async_client.EndpointServiceAsyncClient", "kind": "Gdef"}, "EndpointServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.endpoint_service.client.EndpointServiceClient", "kind": "Gdef"}, "EntityIdSelector": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.EntityIdSelector", "kind": "Gdef"}, "EntityType": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.entity_type.EntityType", "kind": "Gdef"}, "EnvVar": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.env_var.EnvVar", "kind": "Gdef"}, "ErrorAnalysisAnnotation": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluated_annotation.ErrorAnalysisAnnotation", "kind": "Gdef"}, "EvaluateInstancesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.EvaluateInstancesRequest", "kind": "Gdef"}, "EvaluateInstancesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.EvaluateInstancesResponse", "kind": "Gdef"}, "EvaluatedAnnotation": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluated_annotation.EvaluatedAnnotation", "kind": "Gdef"}, "EvaluatedAnnotationExplanation": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluated_annotation.EvaluatedAnnotationExplanation", "kind": "Gdef"}, "EvaluationServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.async_client.EvaluationServiceAsyncClient", "kind": "Gdef"}, "EvaluationServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.evaluation_service.client.EvaluationServiceClient", "kind": "Gdef"}, "Event": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.event.Event", "kind": "Gdef"}, "ExactMatchInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ExactMatchInput", "kind": "Gdef"}, "ExactMatchInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ExactMatchInstance", "kind": "Gdef"}, "ExactMatchMetricValue": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ExactMatchMetricValue", "kind": "Gdef"}, "ExactMatchResults": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ExactMatchResults", "kind": "Gdef"}, "ExactMatchSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ExactMatchSpec", "kind": "Gdef"}, "Examples": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.Examples", "kind": "Gdef"}, "ExamplesOverride": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.ExamplesOverride", "kind": "Gdef"}, "ExamplesRestrictionsNamespace": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.ExamplesRestrictionsNamespace", "kind": "Gdef"}, "ExecuteExtensionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension_execution_service.ExecuteExtensionRequest", "kind": "Gdef"}, "ExecuteExtensionResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension_execution_service.ExecuteExtensionResponse", "kind": "Gdef"}, "Execution": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.execution.Execution", "kind": "Gdef"}, "ExplainRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.ExplainRequest", "kind": "Gdef"}, "ExplainResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.ExplainResponse", "kind": "Gdef"}, "Explanation": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.Explanation", "kind": "Gdef"}, "ExplanationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation_metadata.ExplanationMetadata", "kind": "Gdef"}, "ExplanationMetadataOverride": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.ExplanationMetadataOverride", "kind": "Gdef"}, "ExplanationParameters": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.ExplanationParameters", "kind": "Gdef"}, "ExplanationSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.ExplanationSpec", "kind": "Gdef"}, "ExplanationSpecOverride": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.ExplanationSpecOverride", "kind": "Gdef"}, "ExportDataConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset.ExportDataConfig", "kind": "Gdef"}, "ExportDataOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ExportDataOperationMetadata", "kind": "Gdef"}, "ExportDataRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ExportDataRequest", "kind": "Gdef"}, "ExportDataResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ExportDataResponse", "kind": "Gdef"}, "ExportFeatureValuesOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.ExportFeatureValuesOperationMetadata", "kind": "Gdef"}, "ExportFeatureValuesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.ExportFeatureValuesRequest", "kind": "Gdef"}, "ExportFeatureValuesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.ExportFeatureValuesResponse", "kind": "Gdef"}, "ExportFractionSplit": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset.ExportFractionSplit", "kind": "Gdef"}, "ExportModelOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.ExportModelOperationMetadata", "kind": "Gdef"}, "ExportModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.ExportModelRequest", "kind": "Gdef"}, "ExportModelResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.ExportModelResponse", "kind": "Gdef"}, "ExportTensorboardTimeSeriesDataRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ExportTensorboardTimeSeriesDataRequest", "kind": "Gdef"}, "ExportTensorboardTimeSeriesDataResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ExportTensorboardTimeSeriesDataResponse", "kind": "Gdef"}, "Extension": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension.Extension", "kind": "Gdef"}, "ExtensionExecutionServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.async_client.ExtensionExecutionServiceAsyncClient", "kind": "Gdef"}, "ExtensionExecutionServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.extension_execution_service.client.ExtensionExecutionServiceClient", "kind": "Gdef"}, "ExtensionManifest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension.ExtensionManifest", "kind": "Gdef"}, "ExtensionOperation": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension.ExtensionOperation", "kind": "Gdef"}, "ExtensionPrivateServiceConnectConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension.ExtensionPrivateServiceConnectConfig", "kind": "Gdef"}, "ExtensionRegistryServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.extension_registry_service.async_client.ExtensionRegistryServiceAsyncClient", "kind": "Gdef"}, "ExtensionRegistryServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.extension_registry_service.client.ExtensionRegistryServiceClient", "kind": "Gdef"}, "Feature": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature.Feature", "kind": "Gdef"}, "FeatureGroup": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_group.FeatureGroup", "kind": "Gdef"}, "FeatureNoiseSigma": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.FeatureNoiseSigma", "kind": "Gdef"}, "FeatureOnlineStore": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store.FeatureOnlineStore", "kind": "Gdef"}, "FeatureOnlineStoreAdminServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_admin_service.async_client.FeatureOnlineStoreAdminServiceAsyncClient", "kind": "Gdef"}, "FeatureOnlineStoreAdminServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_admin_service.client.FeatureOnlineStoreAdminServiceClient", "kind": "Gdef"}, "FeatureOnlineStoreServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.async_client.FeatureOnlineStoreServiceAsyncClient", "kind": "Gdef"}, "FeatureOnlineStoreServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.feature_online_store_service.client.FeatureOnlineStoreServiceClient", "kind": "Gdef"}, "FeatureRegistryServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.async_client.FeatureRegistryServiceAsyncClient", "kind": "Gdef"}, "FeatureRegistryServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.feature_registry_service.client.FeatureRegistryServiceClient", "kind": "Gdef"}, "FeatureSelector": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_selector.FeatureSelector", "kind": "Gdef"}, "FeatureStatsAnomaly": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_monitoring_stats.FeatureStatsAnomaly", "kind": "Gdef"}, "FeatureValue": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_online_service.FeatureValue", "kind": "Gdef"}, "FeatureValueDestination": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.FeatureValueDestination", "kind": "Gdef"}, "FeatureValueList": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_online_service.FeatureValueList", "kind": "Gdef"}, "FeatureView": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_view.FeatureView", "kind": "Gdef"}, "FeatureViewDataFormat": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.FeatureViewDataFormat", "kind": "Gdef"}, "FeatureViewDataKey": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.FeatureViewDataKey", "kind": "Gdef"}, "FeatureViewSync": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_view_sync.FeatureViewSync", "kind": "Gdef"}, "Featurestore": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore.Featurestore", "kind": "Gdef"}, "FeaturestoreMonitoringConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_monitoring.FeaturestoreMonitoringConfig", "kind": "Gdef"}, "FeaturestoreOnlineServingServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.featurestore_online_serving_service.async_client.FeaturestoreOnlineServingServiceAsyncClient", "kind": "Gdef"}, "FeaturestoreOnlineServingServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.featurestore_online_serving_service.client.FeaturestoreOnlineServingServiceClient", "kind": "Gdef"}, "FeaturestoreServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.featurestore_service.async_client.FeaturestoreServiceAsyncClient", "kind": "Gdef"}, "FeaturestoreServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.featurestore_service.client.FeaturestoreServiceClient", "kind": "Gdef"}, "FetchFeatureValuesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.FetchFeatureValuesRequest", "kind": "Gdef"}, "FetchFeatureValuesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.FetchFeatureValuesResponse", "kind": "Gdef"}, "FileData": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.FileData", "kind": "Gdef"}, "FileStatus": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data.FileStatus", "kind": "Gdef"}, "FilterSplit": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.training_pipeline.FilterSplit", "kind": "Gdef"}, "FindNeighborsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.match_service.FindNeighborsRequest", "kind": "Gdef"}, "FindNeighborsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.match_service.FindNeighborsResponse", "kind": "Gdef"}, "FluencyInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.FluencyInput", "kind": "Gdef"}, "FluencyInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.FluencyInstance", "kind": "Gdef"}, "FluencyResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.FluencyResult", "kind": "Gdef"}, "FluencySpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.FluencySpec", "kind": "Gdef"}, "FractionSplit": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.training_pipeline.FractionSplit", "kind": "Gdef"}, "FulfillmentInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.FulfillmentInput", "kind": "Gdef"}, "FulfillmentInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.FulfillmentInstance", "kind": "Gdef"}, "FulfillmentResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.FulfillmentResult", "kind": "Gdef"}, "FulfillmentSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.FulfillmentSpec", "kind": "Gdef"}, "FunctionCall": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tool.FunctionCall", "kind": "Gdef"}, "FunctionCallingConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tool.FunctionCallingConfig", "kind": "Gdef"}, "FunctionDeclaration": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tool.FunctionDeclaration", "kind": "Gdef"}, "FunctionResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tool.FunctionResponse", "kind": "Gdef"}, "GcsDestination": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.io.GcsDestination", "kind": "Gdef"}, "GcsSource": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.io.GcsSource", "kind": "Gdef"}, "GenAiCacheServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.async_client.GenAiCacheServiceAsyncClient", "kind": "Gdef"}, "GenAiCacheServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_cache_service.client.GenAiCacheServiceClient", "kind": "Gdef"}, "GenAiTuningServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_tuning_service.async_client.GenAiTuningServiceAsyncClient", "kind": "Gdef"}, "GenAiTuningServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.gen_ai_tuning_service.client.GenAiTuningServiceClient", "kind": "Gdef"}, "GenerateContentRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.GenerateContentRequest", "kind": "Gdef"}, "GenerateContentResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.GenerateContentResponse", "kind": "Gdef"}, "GenerateVideoResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.GenerateVideoResponse", "kind": "Gdef"}, "GenerationConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.GenerationConfig", "kind": "Gdef"}, "GenericOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.operation.GenericOperationMetadata", "kind": "Gdef"}, "GenieSource": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model.GenieSource", "kind": "Gdef"}, "GetAnnotationSpecRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.GetAnnotationSpecRequest", "kind": "Gdef"}, "GetArtifactRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.GetArtifactRequest", "kind": "Gdef"}, "GetBatchPredictionJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.GetBatchPredictionJobRequest", "kind": "Gdef"}, "GetCachedContentRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.GetCachedContentRequest", "kind": "Gdef"}, "GetContextRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.GetContextRequest", "kind": "Gdef"}, "GetCustomJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.GetCustomJobRequest", "kind": "Gdef"}, "GetDataLabelingJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.GetDataLabelingJobRequest", "kind": "Gdef"}, "GetDatasetRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.GetDatasetRequest", "kind": "Gdef"}, "GetDatasetVersionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.GetDatasetVersionRequest", "kind": "Gdef"}, "GetDeploymentResourcePoolRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.deployment_resource_pool_service.GetDeploymentResourcePoolRequest", "kind": "Gdef"}, "GetEndpointRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.GetEndpointRequest", "kind": "Gdef"}, "GetEntityTypeRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.GetEntityTypeRequest", "kind": "Gdef"}, "GetExecutionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.GetExecutionRequest", "kind": "Gdef"}, "GetExtensionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension_registry_service.GetExtensionRequest", "kind": "Gdef"}, "GetFeatureGroupRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_registry_service.GetFeatureGroupRequest", "kind": "Gdef"}, "GetFeatureOnlineStoreRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.GetFeatureOnlineStoreRequest", "kind": "Gdef"}, "GetFeatureRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.GetFeatureRequest", "kind": "Gdef"}, "GetFeatureViewRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.GetFeatureViewRequest", "kind": "Gdef"}, "GetFeatureViewSyncRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.GetFeatureViewSyncRequest", "kind": "Gdef"}, "GetFeaturestoreRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.GetFeaturestoreRequest", "kind": "Gdef"}, "GetHyperparameterTuningJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.GetHyperparameterTuningJobRequest", "kind": "Gdef"}, "GetIndexEndpointRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.GetIndexEndpointRequest", "kind": "Gdef"}, "GetIndexRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_service.GetIndexRequest", "kind": "Gdef"}, "GetMetadataSchemaRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.GetMetadataSchemaRequest", "kind": "Gdef"}, "GetMetadataStoreRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.GetMetadataStoreRequest", "kind": "Gdef"}, "GetModelDeploymentMonitoringJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.GetModelDeploymentMonitoringJobRequest", "kind": "Gdef"}, "GetModelEvaluationRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.GetModelEvaluationRequest", "kind": "Gdef"}, "GetModelEvaluationSliceRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.GetModelEvaluationSliceRequest", "kind": "Gdef"}, "GetModelMonitorRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.GetModelMonitorRequest", "kind": "Gdef"}, "GetModelMonitoringJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.GetModelMonitoringJobRequest", "kind": "Gdef"}, "GetModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.GetModelRequest", "kind": "Gdef"}, "GetNasJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.GetNasJobRequest", "kind": "Gdef"}, "GetNasTrialDetailRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.GetNasTrialDetailRequest", "kind": "Gdef"}, "GetNotebookExecutionJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.GetNotebookExecutionJobRequest", "kind": "Gdef"}, "GetNotebookRuntimeRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.GetNotebookRuntimeRequest", "kind": "Gdef"}, "GetNotebookRuntimeTemplateRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.GetNotebookRuntimeTemplateRequest", "kind": "Gdef"}, "GetPersistentResourceRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource_service.GetPersistentResourceRequest", "kind": "Gdef"}, "GetPipelineJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.GetPipelineJobRequest", "kind": "Gdef"}, "GetPublisherModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_garden_service.GetPublisherModelRequest", "kind": "Gdef"}, "GetRagCorpusRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.GetRagCorpusRequest", "kind": "Gdef"}, "GetRagFileRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.GetRagFileRequest", "kind": "Gdef"}, "GetReasoningEngineRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.reasoning_engine_service.GetReasoningEngineRequest", "kind": "Gdef"}, "GetScheduleRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.schedule_service.GetScheduleRequest", "kind": "Gdef"}, "GetSpecialistPoolRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.specialist_pool_service.GetSpecialistPoolRequest", "kind": "Gdef"}, "GetStudyRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.GetStudyRequest", "kind": "Gdef"}, "GetTensorboardExperimentRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.GetTensorboardExperimentRequest", "kind": "Gdef"}, "GetTensorboardRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.GetTensorboardRequest", "kind": "Gdef"}, "GetTensorboardRunRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.GetTensorboardRunRequest", "kind": "Gdef"}, "GetTensorboardTimeSeriesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.GetTensorboardTimeSeriesRequest", "kind": "Gdef"}, "GetTrainingPipelineRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.GetTrainingPipelineRequest", "kind": "Gdef"}, "GetTrialRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.GetTrialRequest", "kind": "Gdef"}, "GetTuningJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.genai_tuning_service.GetTuningJobRequest", "kind": "Gdef"}, "GoogleDriveSource": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.io.GoogleDriveSource", "kind": "Gdef"}, "GoogleSearchRetrieval": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tool.GoogleSearchRetrieval", "kind": "Gdef"}, "GroundednessInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.GroundednessInput", "kind": "Gdef"}, "GroundednessInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.GroundednessInstance", "kind": "Gdef"}, "GroundednessResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.GroundednessResult", "kind": "Gdef"}, "GroundednessSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.GroundednessSpec", "kind": "Gdef"}, "GroundingChunk": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.GroundingChunk", "kind": "Gdef"}, "GroundingMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.GroundingMetadata", "kind": "Gdef"}, "GroundingSupport": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.GroundingSupport", "kind": "Gdef"}, "HarmCategory": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.HarmCategory", "kind": "Gdef"}, "HttpElementLocation": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension.HttpElementLocation", "kind": "Gdef"}, "HyperparameterTuningJob": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.hyperparameter_tuning_job.HyperparameterTuningJob", "kind": "Gdef"}, "IdMatcher": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_selector.IdMatcher", "kind": "Gdef"}, "ImportDataConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset.ImportDataConfig", "kind": "Gdef"}, "ImportDataOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ImportDataOperationMetadata", "kind": "Gdef"}, "ImportDataRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ImportDataRequest", "kind": "Gdef"}, "ImportDataResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ImportDataResponse", "kind": "Gdef"}, "ImportExtensionOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension_registry_service.ImportExtensionOperationMetadata", "kind": "Gdef"}, "ImportExtensionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension_registry_service.ImportExtensionRequest", "kind": "Gdef"}, "ImportFeatureValuesOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.ImportFeatureValuesOperationMetadata", "kind": "Gdef"}, "ImportFeatureValuesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.ImportFeatureValuesRequest", "kind": "Gdef"}, "ImportFeatureValuesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.ImportFeatureValuesResponse", "kind": "Gdef"}, "ImportModelEvaluationRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.ImportModelEvaluationRequest", "kind": "Gdef"}, "ImportRagFilesConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data.ImportRagFilesConfig", "kind": "Gdef"}, "ImportRagFilesOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ImportRagFilesOperationMetadata", "kind": "Gdef"}, "ImportRagFilesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ImportRagFilesRequest", "kind": "Gdef"}, "ImportRagFilesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ImportRagFilesResponse", "kind": "Gdef"}, "Index": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index.Index", "kind": "Gdef"}, "IndexDatapoint": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index.IndexDatapoint", "kind": "Gdef"}, "IndexEndpoint": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint.IndexEndpoint", "kind": "Gdef"}, "IndexEndpointServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.async_client.IndexEndpointServiceAsyncClient", "kind": "Gdef"}, "IndexEndpointServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.index_endpoint_service.client.IndexEndpointServiceClient", "kind": "Gdef"}, "IndexPrivateEndpoints": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint.IndexPrivateEndpoints", "kind": "Gdef"}, "IndexServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.index_service.async_client.IndexServiceAsyncClient", "kind": "Gdef"}, "IndexServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.index_service.client.IndexServiceClient", "kind": "Gdef"}, "IndexStats": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index.IndexStats", "kind": "Gdef"}, "InputDataConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.training_pipeline.InputDataConfig", "kind": "Gdef"}, "Int64Array": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.types.Int64Array", "kind": "Gdef"}, "IntegratedGradientsAttribution": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.IntegratedGradientsAttribution", "kind": "Gdef"}, "JiraSource": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.io.JiraSource", "kind": "Gdef"}, "JobServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.job_service.async_client.JobServiceAsyncClient", "kind": "Gdef"}, "JobServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.job_service.client.JobServiceClient", "kind": "Gdef"}, "JobState": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_state.JobState", "kind": "Gdef"}, "LargeModelReference": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model.LargeModelReference", "kind": "Gdef"}, "LineageSubgraph": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.lineage_subgraph.LineageSubgraph", "kind": "Gdef"}, "ListAnnotationsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ListAnnotationsRequest", "kind": "Gdef"}, "ListAnnotationsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ListAnnotationsResponse", "kind": "Gdef"}, "ListArtifactsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.ListArtifactsRequest", "kind": "Gdef"}, "ListArtifactsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.ListArtifactsResponse", "kind": "Gdef"}, "ListBatchPredictionJobsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ListBatchPredictionJobsRequest", "kind": "Gdef"}, "ListBatchPredictionJobsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ListBatchPredictionJobsResponse", "kind": "Gdef"}, "ListCachedContentsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.ListCachedContentsRequest", "kind": "Gdef"}, "ListCachedContentsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.ListCachedContentsResponse", "kind": "Gdef"}, "ListContextsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.ListContextsRequest", "kind": "Gdef"}, "ListContextsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.ListContextsResponse", "kind": "Gdef"}, "ListCustomJobsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ListCustomJobsRequest", "kind": "Gdef"}, "ListCustomJobsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ListCustomJobsResponse", "kind": "Gdef"}, "ListDataItemsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ListDataItemsRequest", "kind": "Gdef"}, "ListDataItemsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ListDataItemsResponse", "kind": "Gdef"}, "ListDataLabelingJobsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ListDataLabelingJobsRequest", "kind": "Gdef"}, "ListDataLabelingJobsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ListDataLabelingJobsResponse", "kind": "Gdef"}, "ListDatasetVersionsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ListDatasetVersionsRequest", "kind": "Gdef"}, "ListDatasetVersionsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ListDatasetVersionsResponse", "kind": "Gdef"}, "ListDatasetsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ListDatasetsRequest", "kind": "Gdef"}, "ListDatasetsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ListDatasetsResponse", "kind": "Gdef"}, "ListDeploymentResourcePoolsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.deployment_resource_pool_service.ListDeploymentResourcePoolsRequest", "kind": "Gdef"}, "ListDeploymentResourcePoolsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.deployment_resource_pool_service.ListDeploymentResourcePoolsResponse", "kind": "Gdef"}, "ListEndpointsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.ListEndpointsRequest", "kind": "Gdef"}, "ListEndpointsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.ListEndpointsResponse", "kind": "Gdef"}, "ListEntityTypesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.ListEntityTypesRequest", "kind": "Gdef"}, "ListEntityTypesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.ListEntityTypesResponse", "kind": "Gdef"}, "ListExecutionsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.ListExecutionsRequest", "kind": "Gdef"}, "ListExecutionsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.ListExecutionsResponse", "kind": "Gdef"}, "ListExtensionsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension_registry_service.ListExtensionsRequest", "kind": "Gdef"}, "ListExtensionsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension_registry_service.ListExtensionsResponse", "kind": "Gdef"}, "ListFeatureGroupsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_registry_service.ListFeatureGroupsRequest", "kind": "Gdef"}, "ListFeatureGroupsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_registry_service.ListFeatureGroupsResponse", "kind": "Gdef"}, "ListFeatureOnlineStoresRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.ListFeatureOnlineStoresRequest", "kind": "Gdef"}, "ListFeatureOnlineStoresResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.ListFeatureOnlineStoresResponse", "kind": "Gdef"}, "ListFeatureViewSyncsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.ListFeatureViewSyncsRequest", "kind": "Gdef"}, "ListFeatureViewSyncsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.ListFeatureViewSyncsResponse", "kind": "Gdef"}, "ListFeatureViewsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.ListFeatureViewsRequest", "kind": "Gdef"}, "ListFeatureViewsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.ListFeatureViewsResponse", "kind": "Gdef"}, "ListFeaturesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.ListFeaturesRequest", "kind": "Gdef"}, "ListFeaturesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.ListFeaturesResponse", "kind": "Gdef"}, "ListFeaturestoresRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.ListFeaturestoresRequest", "kind": "Gdef"}, "ListFeaturestoresResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.ListFeaturestoresResponse", "kind": "Gdef"}, "ListHyperparameterTuningJobsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ListHyperparameterTuningJobsRequest", "kind": "Gdef"}, "ListHyperparameterTuningJobsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ListHyperparameterTuningJobsResponse", "kind": "Gdef"}, "ListIndexEndpointsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.ListIndexEndpointsRequest", "kind": "Gdef"}, "ListIndexEndpointsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.ListIndexEndpointsResponse", "kind": "Gdef"}, "ListIndexesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_service.ListIndexesRequest", "kind": "Gdef"}, "ListIndexesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_service.ListIndexesResponse", "kind": "Gdef"}, "ListMetadataSchemasRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.ListMetadataSchemasRequest", "kind": "Gdef"}, "ListMetadataSchemasResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.ListMetadataSchemasResponse", "kind": "Gdef"}, "ListMetadataStoresRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.ListMetadataStoresRequest", "kind": "Gdef"}, "ListMetadataStoresResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.ListMetadataStoresResponse", "kind": "Gdef"}, "ListModelDeploymentMonitoringJobsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ListModelDeploymentMonitoringJobsRequest", "kind": "Gdef"}, "ListModelDeploymentMonitoringJobsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ListModelDeploymentMonitoringJobsResponse", "kind": "Gdef"}, "ListModelEvaluationSlicesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.ListModelEvaluationSlicesRequest", "kind": "Gdef"}, "ListModelEvaluationSlicesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.ListModelEvaluationSlicesResponse", "kind": "Gdef"}, "ListModelEvaluationsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.ListModelEvaluationsRequest", "kind": "Gdef"}, "ListModelEvaluationsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.ListModelEvaluationsResponse", "kind": "Gdef"}, "ListModelMonitoringJobsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.ListModelMonitoringJobsRequest", "kind": "Gdef"}, "ListModelMonitoringJobsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.ListModelMonitoringJobsResponse", "kind": "Gdef"}, "ListModelMonitorsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.ListModelMonitorsRequest", "kind": "Gdef"}, "ListModelMonitorsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.ListModelMonitorsResponse", "kind": "Gdef"}, "ListModelVersionsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.ListModelVersionsRequest", "kind": "Gdef"}, "ListModelVersionsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.ListModelVersionsResponse", "kind": "Gdef"}, "ListModelsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.ListModelsRequest", "kind": "Gdef"}, "ListModelsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.ListModelsResponse", "kind": "Gdef"}, "ListNasJobsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ListNasJobsRequest", "kind": "Gdef"}, "ListNasJobsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ListNasJobsResponse", "kind": "Gdef"}, "ListNasTrialDetailsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ListNasTrialDetailsRequest", "kind": "Gdef"}, "ListNasTrialDetailsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ListNasTrialDetailsResponse", "kind": "Gdef"}, "ListNotebookExecutionJobsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.ListNotebookExecutionJobsRequest", "kind": "Gdef"}, "ListNotebookExecutionJobsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.ListNotebookExecutionJobsResponse", "kind": "Gdef"}, "ListNotebookRuntimeTemplatesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.ListNotebookRuntimeTemplatesRequest", "kind": "Gdef"}, "ListNotebookRuntimeTemplatesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.ListNotebookRuntimeTemplatesResponse", "kind": "Gdef"}, "ListNotebookRuntimesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.ListNotebookRuntimesRequest", "kind": "Gdef"}, "ListNotebookRuntimesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.ListNotebookRuntimesResponse", "kind": "Gdef"}, "ListOptimalTrialsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.ListOptimalTrialsRequest", "kind": "Gdef"}, "ListOptimalTrialsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.ListOptimalTrialsResponse", "kind": "Gdef"}, "ListPersistentResourcesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource_service.ListPersistentResourcesRequest", "kind": "Gdef"}, "ListPersistentResourcesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource_service.ListPersistentResourcesResponse", "kind": "Gdef"}, "ListPipelineJobsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.ListPipelineJobsRequest", "kind": "Gdef"}, "ListPipelineJobsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.ListPipelineJobsResponse", "kind": "Gdef"}, "ListPublisherModelsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_garden_service.ListPublisherModelsRequest", "kind": "Gdef"}, "ListPublisherModelsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_garden_service.ListPublisherModelsResponse", "kind": "Gdef"}, "ListRagCorporaRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ListRagCorporaRequest", "kind": "Gdef"}, "ListRagCorporaResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ListRagCorporaResponse", "kind": "Gdef"}, "ListRagFilesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ListRagFilesRequest", "kind": "Gdef"}, "ListRagFilesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.ListRagFilesResponse", "kind": "Gdef"}, "ListReasoningEnginesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.reasoning_engine_service.ListReasoningEnginesRequest", "kind": "Gdef"}, "ListReasoningEnginesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.reasoning_engine_service.ListReasoningEnginesResponse", "kind": "Gdef"}, "ListSavedQueriesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ListSavedQueriesRequest", "kind": "Gdef"}, "ListSavedQueriesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.ListSavedQueriesResponse", "kind": "Gdef"}, "ListSchedulesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.schedule_service.ListSchedulesRequest", "kind": "Gdef"}, "ListSchedulesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.schedule_service.ListSchedulesResponse", "kind": "Gdef"}, "ListSpecialistPoolsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.specialist_pool_service.ListSpecialistPoolsRequest", "kind": "Gdef"}, "ListSpecialistPoolsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.specialist_pool_service.ListSpecialistPoolsResponse", "kind": "Gdef"}, "ListStudiesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.ListStudiesRequest", "kind": "Gdef"}, "ListStudiesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.ListStudiesResponse", "kind": "Gdef"}, "ListTensorboardExperimentsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ListTensorboardExperimentsRequest", "kind": "Gdef"}, "ListTensorboardExperimentsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ListTensorboardExperimentsResponse", "kind": "Gdef"}, "ListTensorboardRunsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ListTensorboardRunsRequest", "kind": "Gdef"}, "ListTensorboardRunsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ListTensorboardRunsResponse", "kind": "Gdef"}, "ListTensorboardTimeSeriesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ListTensorboardTimeSeriesRequest", "kind": "Gdef"}, "ListTensorboardTimeSeriesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ListTensorboardTimeSeriesResponse", "kind": "Gdef"}, "ListTensorboardsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ListTensorboardsRequest", "kind": "Gdef"}, "ListTensorboardsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ListTensorboardsResponse", "kind": "Gdef"}, "ListTrainingPipelinesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.ListTrainingPipelinesRequest", "kind": "Gdef"}, "ListTrainingPipelinesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_service.ListTrainingPipelinesResponse", "kind": "Gdef"}, "ListTrialsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.ListTrialsRequest", "kind": "Gdef"}, "ListTrialsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.ListTrialsResponse", "kind": "Gdef"}, "ListTuningJobsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.genai_tuning_service.ListTuningJobsRequest", "kind": "Gdef"}, "ListTuningJobsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.genai_tuning_service.ListTuningJobsResponse", "kind": "Gdef"}, "LlmUtilityServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.llm_utility_service.async_client.LlmUtilityServiceAsyncClient", "kind": "Gdef"}, "LlmUtilityServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.llm_utility_service.client.LlmUtilityServiceClient", "kind": "Gdef"}, "LogprobsResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.LogprobsResult", "kind": "Gdef"}, "LookupStudyRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.LookupStudyRequest", "kind": "Gdef"}, "MachineSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.machine_resources.MachineSpec", "kind": "Gdef"}, "ManualBatchTuningParameters": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.manual_batch_tuning_parameters.ManualBatchTuningParameters", "kind": "Gdef"}, "MatchServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.match_service.async_client.MatchServiceAsyncClient", "kind": "Gdef"}, "MatchServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.match_service.client.MatchServiceClient", "kind": "Gdef"}, "Measurement": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.study.Measurement", "kind": "Gdef"}, "MergeVersionAliasesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.MergeVersionAliasesRequest", "kind": "Gdef"}, "MetadataSchema": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_schema.MetadataSchema", "kind": "Gdef"}, "MetadataServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.metadata_service.async_client.MetadataServiceAsyncClient", "kind": "Gdef"}, "MetadataServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.metadata_service.client.MetadataServiceClient", "kind": "Gdef"}, "MetadataStore": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_store.MetadataStore", "kind": "Gdef"}, "MigratableResource": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.migratable_resource.MigratableResource", "kind": "Gdef"}, "MigrateResourceRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.migration_service.MigrateResourceRequest", "kind": "Gdef"}, "MigrateResourceResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.migration_service.MigrateResourceResponse", "kind": "Gdef"}, "MigrationServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.migration_service.async_client.MigrationServiceAsyncClient", "kind": "Gdef"}, "MigrationServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.migration_service.client.MigrationServiceClient", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model.Model", "kind": "Gdef"}, "ModelContainerSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model.ModelContainerSpec", "kind": "Gdef"}, "ModelDeploymentMonitoringBigQueryTable": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_deployment_monitoring_job.ModelDeploymentMonitoringBigQueryTable", "kind": "Gdef"}, "ModelDeploymentMonitoringJob": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_deployment_monitoring_job.ModelDeploymentMonitoringJob", "kind": "Gdef"}, "ModelDeploymentMonitoringObjectiveConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_deployment_monitoring_job.ModelDeploymentMonitoringObjectiveConfig", "kind": "Gdef"}, "ModelDeploymentMonitoringObjectiveType": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_deployment_monitoring_job.ModelDeploymentMonitoringObjectiveType", "kind": "Gdef"}, "ModelDeploymentMonitoringScheduleConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_deployment_monitoring_job.ModelDeploymentMonitoringScheduleConfig", "kind": "Gdef"}, "ModelEvaluation": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_evaluation.ModelEvaluation", "kind": "Gdef"}, "ModelEvaluationSlice": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_evaluation_slice.ModelEvaluationSlice", "kind": "Gdef"}, "ModelExplanation": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.ModelExplanation", "kind": "Gdef"}, "ModelGardenServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.model_garden_service.async_client.ModelGardenServiceAsyncClient", "kind": "Gdef"}, "ModelGardenServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.model_garden_service.client.ModelGardenServiceClient", "kind": "Gdef"}, "ModelGardenSource": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model.ModelGardenSource", "kind": "Gdef"}, "ModelMonitor": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitor.ModelMonitor", "kind": "Gdef"}, "ModelMonitoringAlert": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_alert.ModelMonitoringAlert", "kind": "Gdef"}, "ModelMonitoringAlertCondition": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_alert.ModelMonitoringAlertCondition", "kind": "Gdef"}, "ModelMonitoringAlertConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring.ModelMonitoringAlertConfig", "kind": "Gdef"}, "ModelMonitoringAnomaly": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_alert.ModelMonitoringAnomaly", "kind": "Gdef"}, "ModelMonitoringConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring.ModelMonitoringConfig", "kind": "Gdef"}, "ModelMonitoringInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_spec.ModelMonitoringInput", "kind": "Gdef"}, "ModelMonitoringJob": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_job.ModelMonitoringJob", "kind": "Gdef"}, "ModelMonitoringJobExecutionDetail": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_job.ModelMonitoringJobExecutionDetail", "kind": "Gdef"}, "ModelMonitoringNotificationSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_spec.ModelMonitoringNotificationSpec", "kind": "Gdef"}, "ModelMonitoringObjectiveConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring.ModelMonitoringObjectiveConfig", "kind": "Gdef"}, "ModelMonitoringObjectiveSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_spec.ModelMonitoringObjectiveSpec", "kind": "Gdef"}, "ModelMonitoringOutputSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_spec.ModelMonitoringOutputSpec", "kind": "Gdef"}, "ModelMonitoringSchema": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitor.ModelMonitoringSchema", "kind": "Gdef"}, "ModelMonitoringServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.model_monitoring_service.async_client.ModelMonitoringServiceAsyncClient", "kind": "Gdef"}, "ModelMonitoringServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.model_monitoring_service.client.ModelMonitoringServiceClient", "kind": "Gdef"}, "ModelMonitoringSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_spec.ModelMonitoringSpec", "kind": "Gdef"}, "ModelMonitoringStats": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_stats.ModelMonitoringStats", "kind": "Gdef"}, "ModelMonitoringStatsAnomalies": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_deployment_monitoring_job.ModelMonitoringStatsAnomalies", "kind": "Gdef"}, "ModelMonitoringStatsDataPoint": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_stats.ModelMonitoringStatsDataPoint", "kind": "Gdef"}, "ModelMonitoringTabularStats": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_stats.ModelMonitoringTabularStats", "kind": "Gdef"}, "ModelServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.model_service.async_client.ModelServiceAsyncClient", "kind": "Gdef"}, "ModelServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.model_service.client.ModelServiceClient", "kind": "Gdef"}, "ModelSourceInfo": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model.ModelSourceInfo", "kind": "Gdef"}, "MutateDeployedIndexOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.MutateDeployedIndexOperationMetadata", "kind": "Gdef"}, "MutateDeployedIndexRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.MutateDeployedIndexRequest", "kind": "Gdef"}, "MutateDeployedIndexResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.MutateDeployedIndexResponse", "kind": "Gdef"}, "MutateDeployedModelOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.MutateDeployedModelOperationMetadata", "kind": "Gdef"}, "MutateDeployedModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.MutateDeployedModelRequest", "kind": "Gdef"}, "MutateDeployedModelResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.MutateDeployedModelResponse", "kind": "Gdef"}, "NasJob": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.nas_job.NasJob", "kind": "Gdef"}, "NasJobOutput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.nas_job.NasJobOutput", "kind": "Gdef"}, "NasJobSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.nas_job.NasJobSpec", "kind": "Gdef"}, "NasTrial": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.nas_job.NasTrial", "kind": "Gdef"}, "NasTrialDetail": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.nas_job.NasTrialDetail", "kind": "Gdef"}, "NearestNeighborQuery": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.NearestNeighborQuery", "kind": "Gdef"}, "NearestNeighborSearchOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_service.NearestNeighborSearchOperationMetadata", "kind": "Gdef"}, "NearestNeighbors": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.NearestNeighbors", "kind": "Gdef"}, "Neighbor": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.Neighbor", "kind": "Gdef"}, "NetworkSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.network_spec.NetworkSpec", "kind": "Gdef"}, "NfsMount": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.machine_resources.NfsMount", "kind": "Gdef"}, "NotebookEucConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_euc_config.NotebookEucConfig", "kind": "Gdef"}, "NotebookExecutionJob": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_execution_job.NotebookExecutionJob", "kind": "Gdef"}, "NotebookExecutionJobView": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.NotebookExecutionJobView", "kind": "Gdef"}, "NotebookIdleShutdownConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_idle_shutdown_config.NotebookIdleShutdownConfig", "kind": "Gdef"}, "NotebookRuntime": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_runtime.NotebookRuntime", "kind": "Gdef"}, "NotebookRuntimeTemplate": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_runtime.NotebookRuntimeTemplate", "kind": "Gdef"}, "NotebookRuntimeTemplateRef": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_runtime_template_ref.NotebookRuntimeTemplateRef", "kind": "Gdef"}, "NotebookRuntimeType": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_runtime.NotebookRuntimeType", "kind": "Gdef"}, "NotebookServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.notebook_service.async_client.NotebookServiceAsyncClient", "kind": "Gdef"}, "NotebookServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.notebook_service.client.NotebookServiceClient", "kind": "Gdef"}, "PSCAutomationConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.service_networking.PSCAutomationConfig", "kind": "Gdef"}, "PairwiseChoice": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PairwiseChoice", "kind": "Gdef"}, "PairwiseMetricInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PairwiseMetricInput", "kind": "Gdef"}, "PairwiseMetricInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PairwiseMetricInstance", "kind": "Gdef"}, "PairwiseMetricResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PairwiseMetricResult", "kind": "Gdef"}, "PairwiseMetricSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PairwiseMetricSpec", "kind": "Gdef"}, "PairwiseQuestionAnsweringQualityInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PairwiseQuestionAnsweringQualityInput", "kind": "Gdef"}, "PairwiseQuestionAnsweringQualityInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PairwiseQuestionAnsweringQualityInstance", "kind": "Gdef"}, "PairwiseQuestionAnsweringQualityResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PairwiseQuestionAnsweringQualityResult", "kind": "Gdef"}, "PairwiseQuestionAnsweringQualitySpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PairwiseQuestionAnsweringQualitySpec", "kind": "Gdef"}, "PairwiseSummarizationQualityInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PairwiseSummarizationQualityInput", "kind": "Gdef"}, "PairwiseSummarizationQualityInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PairwiseSummarizationQualityInstance", "kind": "Gdef"}, "PairwiseSummarizationQualityResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PairwiseSummarizationQualityResult", "kind": "Gdef"}, "PairwiseSummarizationQualitySpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PairwiseSummarizationQualitySpec", "kind": "Gdef"}, "Part": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.Part", "kind": "Gdef"}, "PartnerModelTuningSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tuning_job.PartnerModelTuningSpec", "kind": "Gdef"}, "PauseModelDeploymentMonitoringJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.PauseModelDeploymentMonitoringJobRequest", "kind": "Gdef"}, "PauseScheduleRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.schedule_service.PauseScheduleRequest", "kind": "Gdef"}, "PersistentDiskSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.machine_resources.PersistentDiskSpec", "kind": "Gdef"}, "PersistentResource": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource.PersistentResource", "kind": "Gdef"}, "PersistentResourceServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.persistent_resource_service.async_client.PersistentResourceServiceAsyncClient", "kind": "Gdef"}, "PersistentResourceServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.persistent_resource_service.client.PersistentResourceServiceClient", "kind": "Gdef"}, "PipelineFailurePolicy": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_failure_policy.PipelineFailurePolicy", "kind": "Gdef"}, "PipelineJob": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_job.PipelineJob", "kind": "Gdef"}, "PipelineJobDetail": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_job.PipelineJobDetail", "kind": "Gdef"}, "PipelineServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.pipeline_service.async_client.PipelineServiceAsyncClient", "kind": "Gdef"}, "PipelineServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.pipeline_service.client.PipelineServiceClient", "kind": "Gdef"}, "PipelineState": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_state.PipelineState", "kind": "Gdef"}, "PipelineTaskDetail": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_job.PipelineTaskDetail", "kind": "Gdef"}, "PipelineTaskExecutorDetail": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_job.PipelineTaskExecutorDetail", "kind": "Gdef"}, "PipelineTaskRerunConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_job.PipelineTaskRerunConfig", "kind": "Gdef"}, "PipelineTemplateMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.pipeline_job.PipelineTemplateMetadata", "kind": "Gdef"}, "PointwiseMetricInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PointwiseMetricInput", "kind": "Gdef"}, "PointwiseMetricInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PointwiseMetricInstance", "kind": "Gdef"}, "PointwiseMetricResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PointwiseMetricResult", "kind": "Gdef"}, "PointwiseMetricSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.PointwiseMetricSpec", "kind": "Gdef"}, "Port": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model.Port", "kind": "Gdef"}, "PredefinedSplit": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.training_pipeline.PredefinedSplit", "kind": "Gdef"}, "PredictLongRunningMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.PredictLongRunningMetadata", "kind": "Gdef"}, "PredictLongRunningResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.PredictLongRunningResponse", "kind": "Gdef"}, "PredictRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.PredictRequest", "kind": "Gdef"}, "PredictRequestResponseLoggingConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint.PredictRequestResponseLoggingConfig", "kind": "Gdef"}, "PredictResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.PredictResponse", "kind": "Gdef"}, "PredictSchemata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model.PredictSchemata", "kind": "Gdef"}, "PredictionServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.prediction_service.async_client.PredictionServiceAsyncClient", "kind": "Gdef"}, "PredictionServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.prediction_service.client.PredictionServiceClient", "kind": "Gdef"}, "Presets": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.Presets", "kind": "Gdef"}, "PrivateEndpoints": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint.PrivateEndpoints", "kind": "Gdef"}, "PrivateServiceConnectConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.service_networking.PrivateServiceConnectConfig", "kind": "Gdef"}, "Probe": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model.Probe", "kind": "Gdef"}, "PscAutomatedEndpoints": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.service_networking.PscAutomatedEndpoints", "kind": "Gdef"}, "PscInterfaceConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.service_networking.PscInterfaceConfig", "kind": "Gdef"}, "PublisherModel": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.publisher_model.PublisherModel", "kind": "Gdef"}, "PublisherModelView": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_garden_service.PublisherModelView", "kind": "Gdef"}, "PurgeArtifactsMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.PurgeArtifactsMetadata", "kind": "Gdef"}, "PurgeArtifactsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.PurgeArtifactsRequest", "kind": "Gdef"}, "PurgeArtifactsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.PurgeArtifactsResponse", "kind": "Gdef"}, "PurgeContextsMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.PurgeContextsMetadata", "kind": "Gdef"}, "PurgeContextsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.PurgeContextsRequest", "kind": "Gdef"}, "PurgeContextsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.PurgeContextsResponse", "kind": "Gdef"}, "PurgeExecutionsMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.PurgeExecutionsMetadata", "kind": "Gdef"}, "PurgeExecutionsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.PurgeExecutionsRequest", "kind": "Gdef"}, "PurgeExecutionsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.PurgeExecutionsResponse", "kind": "Gdef"}, "PythonPackageSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.custom_job.PythonPackageSpec", "kind": "Gdef"}, "QueryArtifactLineageSubgraphRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.QueryArtifactLineageSubgraphRequest", "kind": "Gdef"}, "QueryContextLineageSubgraphRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.QueryContextLineageSubgraphRequest", "kind": "Gdef"}, "QueryDeployedModelsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.deployment_resource_pool_service.QueryDeployedModelsRequest", "kind": "Gdef"}, "QueryDeployedModelsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.deployment_resource_pool_service.QueryDeployedModelsResponse", "kind": "Gdef"}, "QueryExecutionInputsAndOutputsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.QueryExecutionInputsAndOutputsRequest", "kind": "Gdef"}, "QueryExtensionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension_execution_service.QueryExtensionRequest", "kind": "Gdef"}, "QueryExtensionResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension_execution_service.QueryExtensionResponse", "kind": "Gdef"}, "QueryReasoningEngineRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.reasoning_engine_execution_service.QueryReasoningEngineRequest", "kind": "Gdef"}, "QueryReasoningEngineResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.reasoning_engine_execution_service.QueryReasoningEngineResponse", "kind": "Gdef"}, "QuestionAnsweringCorrectnessInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringCorrectnessInput", "kind": "Gdef"}, "QuestionAnsweringCorrectnessInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringCorrectnessInstance", "kind": "Gdef"}, "QuestionAnsweringCorrectnessResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringCorrectnessResult", "kind": "Gdef"}, "QuestionAnsweringCorrectnessSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringCorrectnessSpec", "kind": "Gdef"}, "QuestionAnsweringHelpfulnessInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringHelpfulnessInput", "kind": "Gdef"}, "QuestionAnsweringHelpfulnessInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringHelpfulnessInstance", "kind": "Gdef"}, "QuestionAnsweringHelpfulnessResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringHelpfulnessResult", "kind": "Gdef"}, "QuestionAnsweringHelpfulnessSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringHelpfulnessSpec", "kind": "Gdef"}, "QuestionAnsweringQualityInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringQualityInput", "kind": "Gdef"}, "QuestionAnsweringQualityInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringQualityInstance", "kind": "Gdef"}, "QuestionAnsweringQualityResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringQualityResult", "kind": "Gdef"}, "QuestionAnsweringQualitySpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringQualitySpec", "kind": "Gdef"}, "QuestionAnsweringRelevanceInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringRelevanceInput", "kind": "Gdef"}, "QuestionAnsweringRelevanceInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringRelevanceInstance", "kind": "Gdef"}, "QuestionAnsweringRelevanceResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringRelevanceResult", "kind": "Gdef"}, "QuestionAnsweringRelevanceSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.QuestionAnsweringRelevanceSpec", "kind": "Gdef"}, "RagContexts": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_service.RagContexts", "kind": "Gdef"}, "RagCorpus": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data.RagCorpus", "kind": "Gdef"}, "RagEmbeddingModelConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data.RagEmbeddingModelConfig", "kind": "Gdef"}, "RagFile": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data.RagFile", "kind": "Gdef"}, "RagFileChunkingConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data.RagFileChunkingConfig", "kind": "Gdef"}, "RagFileParsingConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data.RagFileParsingConfig", "kind": "Gdef"}, "RagQuery": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_service.RagQuery", "kind": "Gdef"}, "RagVectorDbConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data.RagVectorDbConfig", "kind": "Gdef"}, "RawPredictRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.RawPredictRequest", "kind": "Gdef"}, "RayLogsSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource.RayLogsSpec", "kind": "Gdef"}, "RayMetricSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource.RayMetricSpec", "kind": "Gdef"}, "RaySpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource.RaySpec", "kind": "Gdef"}, "ReadFeatureValuesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_online_service.ReadFeatureValuesRequest", "kind": "Gdef"}, "ReadFeatureValuesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_online_service.ReadFeatureValuesResponse", "kind": "Gdef"}, "ReadIndexDatapointsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.match_service.ReadIndexDatapointsRequest", "kind": "Gdef"}, "ReadIndexDatapointsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.match_service.ReadIndexDatapointsResponse", "kind": "Gdef"}, "ReadTensorboardBlobDataRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ReadTensorboardBlobDataRequest", "kind": "Gdef"}, "ReadTensorboardBlobDataResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ReadTensorboardBlobDataResponse", "kind": "Gdef"}, "ReadTensorboardSizeRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ReadTensorboardSizeRequest", "kind": "Gdef"}, "ReadTensorboardSizeResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ReadTensorboardSizeResponse", "kind": "Gdef"}, "ReadTensorboardTimeSeriesDataRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ReadTensorboardTimeSeriesDataRequest", "kind": "Gdef"}, "ReadTensorboardTimeSeriesDataResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ReadTensorboardTimeSeriesDataResponse", "kind": "Gdef"}, "ReadTensorboardUsageRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ReadTensorboardUsageRequest", "kind": "Gdef"}, "ReadTensorboardUsageResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.ReadTensorboardUsageResponse", "kind": "Gdef"}, "ReasoningEngine": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.reasoning_engine.ReasoningEngine", "kind": "Gdef"}, "ReasoningEngineExecutionServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.reasoning_engine_execution_service.async_client.ReasoningEngineExecutionServiceAsyncClient", "kind": "Gdef"}, "ReasoningEngineExecutionServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.reasoning_engine_execution_service.client.ReasoningEngineExecutionServiceClient", "kind": "Gdef"}, "ReasoningEngineServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.reasoning_engine_service.async_client.ReasoningEngineServiceAsyncClient", "kind": "Gdef"}, "ReasoningEngineServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.reasoning_engine_service.client.ReasoningEngineServiceClient", "kind": "Gdef"}, "ReasoningEngineSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.reasoning_engine.ReasoningEngineSpec", "kind": "Gdef"}, "RebaseTunedModelOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.genai_tuning_service.RebaseTunedModelOperationMetadata", "kind": "Gdef"}, "RebaseTunedModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.genai_tuning_service.RebaseTunedModelRequest", "kind": "Gdef"}, "RebootPersistentResourceOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource_service.RebootPersistentResourceOperationMetadata", "kind": "Gdef"}, "RebootPersistentResourceRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource_service.RebootPersistentResourceRequest", "kind": "Gdef"}, "RemoveContextChildrenRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.RemoveContextChildrenRequest", "kind": "Gdef"}, "RemoveContextChildrenResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.RemoveContextChildrenResponse", "kind": "Gdef"}, "RemoveDatapointsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_service.RemoveDatapointsRequest", "kind": "Gdef"}, "RemoveDatapointsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_service.RemoveDatapointsResponse", "kind": "Gdef"}, "ReservationAffinity": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.reservation_affinity.ReservationAffinity", "kind": "Gdef"}, "ResourcePool": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource.ResourcePool", "kind": "Gdef"}, "ResourceRuntime": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource.ResourceRuntime", "kind": "Gdef"}, "ResourceRuntimeSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource.ResourceRuntimeSpec", "kind": "Gdef"}, "ResourcesConsumed": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.machine_resources.ResourcesConsumed", "kind": "Gdef"}, "RestoreDatasetVersionOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.RestoreDatasetVersionOperationMetadata", "kind": "Gdef"}, "RestoreDatasetVersionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.RestoreDatasetVersionRequest", "kind": "Gdef"}, "ResumeModelDeploymentMonitoringJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.ResumeModelDeploymentMonitoringJobRequest", "kind": "Gdef"}, "ResumeScheduleRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.schedule_service.ResumeScheduleRequest", "kind": "Gdef"}, "Retrieval": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tool.Retrieval", "kind": "Gdef"}, "RetrievalMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.RetrievalMetadata", "kind": "Gdef"}, "RetrieveContextsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_service.RetrieveContextsRequest", "kind": "Gdef"}, "RetrieveContextsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_service.RetrieveContextsResponse", "kind": "Gdef"}, "RougeInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.RougeInput", "kind": "Gdef"}, "RougeInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.RougeInstance", "kind": "Gdef"}, "RougeMetricValue": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.RougeMetricValue", "kind": "Gdef"}, "RougeResults": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.RougeResults", "kind": "Gdef"}, "RougeSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.RougeSpec", "kind": "Gdef"}, "RuntimeArtifact": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.ui_pipeline_spec.RuntimeArtifact", "kind": "Gdef"}, "RuntimeConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension.RuntimeConfig", "kind": "Gdef"}, "SafetyInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SafetyInput", "kind": "Gdef"}, "SafetyInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SafetyInstance", "kind": "Gdef"}, "SafetyRating": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.SafetyRating", "kind": "Gdef"}, "SafetyResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SafetyResult", "kind": "Gdef"}, "SafetySetting": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.SafetySetting", "kind": "Gdef"}, "SafetySpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SafetySpec", "kind": "Gdef"}, "SampleConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.data_labeling_job.SampleConfig", "kind": "Gdef"}, "SampledShapleyAttribution": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.SampledShapleyAttribution", "kind": "Gdef"}, "SamplingStrategy": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring.SamplingStrategy", "kind": "Gdef"}, "SavedQuery": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.saved_query.SavedQuery", "kind": "Gdef"}, "Scalar": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_data.Scalar", "kind": "Gdef"}, "Schedule": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.schedule.Schedule", "kind": "Gdef"}, "ScheduleServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.schedule_service.async_client.ScheduleServiceAsyncClient", "kind": "Gdef"}, "ScheduleServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.schedule_service.client.ScheduleServiceClient", "kind": "Gdef"}, "Scheduling": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.custom_job.Scheduling", "kind": "Gdef"}, "Schema": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.openapi.Schema", "kind": "Gdef"}, "SearchDataItemsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.SearchDataItemsRequest", "kind": "Gdef"}, "SearchDataItemsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.SearchDataItemsResponse", "kind": "Gdef"}, "SearchEntryPoint": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.SearchEntryPoint", "kind": "Gdef"}, "SearchFeaturesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.SearchFeaturesRequest", "kind": "Gdef"}, "SearchFeaturesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.SearchFeaturesResponse", "kind": "Gdef"}, "SearchMigratableResourcesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.migration_service.SearchMigratableResourcesRequest", "kind": "Gdef"}, "SearchMigratableResourcesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.migration_service.SearchMigratableResourcesResponse", "kind": "Gdef"}, "SearchModelDeploymentMonitoringStatsAnomaliesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.SearchModelDeploymentMonitoringStatsAnomaliesRequest", "kind": "Gdef"}, "SearchModelDeploymentMonitoringStatsAnomaliesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.SearchModelDeploymentMonitoringStatsAnomaliesResponse", "kind": "Gdef"}, "SearchModelMonitoringAlertsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.SearchModelMonitoringAlertsRequest", "kind": "Gdef"}, "SearchModelMonitoringAlertsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.SearchModelMonitoringAlertsResponse", "kind": "Gdef"}, "SearchModelMonitoringStatsFilter": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_stats.SearchModelMonitoringStatsFilter", "kind": "Gdef"}, "SearchModelMonitoringStatsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.SearchModelMonitoringStatsRequest", "kind": "Gdef"}, "SearchModelMonitoringStatsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.SearchModelMonitoringStatsResponse", "kind": "Gdef"}, "SearchNearestEntitiesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.SearchNearestEntitiesRequest", "kind": "Gdef"}, "SearchNearestEntitiesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.SearchNearestEntitiesResponse", "kind": "Gdef"}, "Segment": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.Segment", "kind": "Gdef"}, "ServiceAccountSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource.ServiceAccountSpec", "kind": "Gdef"}, "SharePointSources": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.io.SharePointSources", "kind": "Gdef"}, "ShieldedVmConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.machine_resources.ShieldedVmConfig", "kind": "Gdef"}, "SlackSource": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.io.SlackSource", "kind": "Gdef"}, "SmoothGradConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.SmoothGradConfig", "kind": "Gdef"}, "SpecialistPool": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.specialist_pool.SpecialistPool", "kind": "Gdef"}, "SpecialistPoolServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.specialist_pool_service.async_client.SpecialistPoolServiceAsyncClient", "kind": "Gdef"}, "SpecialistPoolServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.specialist_pool_service.client.SpecialistPoolServiceClient", "kind": "Gdef"}, "StartNotebookRuntimeOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.StartNotebookRuntimeOperationMetadata", "kind": "Gdef"}, "StartNotebookRuntimeRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.StartNotebookRuntimeRequest", "kind": "Gdef"}, "StartNotebookRuntimeResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.StartNotebookRuntimeResponse", "kind": "Gdef"}, "StopTrialRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.StopTrialRequest", "kind": "Gdef"}, "StratifiedSplit": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.training_pipeline.StratifiedSplit", "kind": "Gdef"}, "StreamDirectPredictRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.StreamDirectPredictRequest", "kind": "Gdef"}, "StreamDirectPredictResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.StreamDirectPredictResponse", "kind": "Gdef"}, "StreamDirectRawPredictRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.StreamDirectRawPredictRequest", "kind": "Gdef"}, "StreamDirectRawPredictResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.StreamDirectRawPredictResponse", "kind": "Gdef"}, "StreamRawPredictRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.StreamRawPredictRequest", "kind": "Gdef"}, "StreamingFetchFeatureValuesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.StreamingFetchFeatureValuesRequest", "kind": "Gdef"}, "StreamingFetchFeatureValuesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_service.StreamingFetchFeatureValuesResponse", "kind": "Gdef"}, "StreamingPredictRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.StreamingPredictRequest", "kind": "Gdef"}, "StreamingPredictResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.StreamingPredictResponse", "kind": "Gdef"}, "StreamingRawPredictRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.StreamingRawPredictRequest", "kind": "Gdef"}, "StreamingRawPredictResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.prediction_service.StreamingRawPredictResponse", "kind": "Gdef"}, "StreamingReadFeatureValuesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_online_service.StreamingReadFeatureValuesRequest", "kind": "Gdef"}, "StringArray": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.types.StringArray", "kind": "Gdef"}, "StructFieldValue": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_online_service.StructFieldValue", "kind": "Gdef"}, "StructValue": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_online_service.StructValue", "kind": "Gdef"}, "Study": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.study.Study", "kind": "Gdef"}, "StudySpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.study.StudySpec", "kind": "Gdef"}, "StudyTimeConstraint": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.study.StudyTimeConstraint", "kind": "Gdef"}, "SuggestTrialsMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.SuggestTrialsMetadata", "kind": "Gdef"}, "SuggestTrialsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.SuggestTrialsRequest", "kind": "Gdef"}, "SuggestTrialsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vizier_service.SuggestTrialsResponse", "kind": "Gdef"}, "SummarizationHelpfulnessInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SummarizationHelpfulnessInput", "kind": "Gdef"}, "SummarizationHelpfulnessInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SummarizationHelpfulnessInstance", "kind": "Gdef"}, "SummarizationHelpfulnessResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SummarizationHelpfulnessResult", "kind": "Gdef"}, "SummarizationHelpfulnessSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SummarizationHelpfulnessSpec", "kind": "Gdef"}, "SummarizationQualityInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SummarizationQualityInput", "kind": "Gdef"}, "SummarizationQualityInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SummarizationQualityInstance", "kind": "Gdef"}, "SummarizationQualityResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SummarizationQualityResult", "kind": "Gdef"}, "SummarizationQualitySpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SummarizationQualitySpec", "kind": "Gdef"}, "SummarizationVerbosityInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SummarizationVerbosityInput", "kind": "Gdef"}, "SummarizationVerbosityInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SummarizationVerbosityInstance", "kind": "Gdef"}, "SummarizationVerbosityResult": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SummarizationVerbosityResult", "kind": "Gdef"}, "SummarizationVerbositySpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.SummarizationVerbositySpec", "kind": "Gdef"}, "SupervisedHyperParameters": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tuning_job.SupervisedHyperParameters", "kind": "Gdef"}, "SupervisedTuningDataStats": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tuning_job.SupervisedTuningDataStats", "kind": "Gdef"}, "SupervisedTuningDatasetDistribution": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tuning_job.SupervisedTuningDatasetDistribution", "kind": "Gdef"}, "SupervisedTuningSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tuning_job.SupervisedTuningSpec", "kind": "Gdef"}, "SyncFeatureViewRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.SyncFeatureViewRequest", "kind": "Gdef"}, "SyncFeatureViewResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.SyncFeatureViewResponse", "kind": "Gdef"}, "TFRecordDestination": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.io.TFRecordDestination", "kind": "Gdef"}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.types.Tensor", "kind": "Gdef"}, "Tensorboard": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard.Tensorboard", "kind": "Gdef"}, "TensorboardBlob": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_data.TensorboardBlob", "kind": "Gdef"}, "TensorboardBlobSequence": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_data.TensorboardBlobSequence", "kind": "Gdef"}, "TensorboardExperiment": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_experiment.TensorboardExperiment", "kind": "Gdef"}, "TensorboardRun": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_run.TensorboardRun", "kind": "Gdef"}, "TensorboardServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.tensorboard_service.async_client.TensorboardServiceAsyncClient", "kind": "Gdef"}, "TensorboardServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.tensorboard_service.client.TensorboardServiceClient", "kind": "Gdef"}, "TensorboardTensor": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_data.TensorboardTensor", "kind": "Gdef"}, "TensorboardTimeSeries": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_time_series.TensorboardTimeSeries", "kind": "Gdef"}, "ThresholdConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring.ThresholdConfig", "kind": "Gdef"}, "TimeSeriesData": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_data.TimeSeriesData", "kind": "Gdef"}, "TimeSeriesDataPoint": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_data.TimeSeriesDataPoint", "kind": "Gdef"}, "TimestampSplit": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.training_pipeline.TimestampSplit", "kind": "Gdef"}, "TokensInfo": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.llm_utility_service.TokensInfo", "kind": "Gdef"}, "Tool": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tool.Tool", "kind": "Gdef"}, "ToolCallValidInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolCallValidInput", "kind": "Gdef"}, "ToolCallValidInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolCallValidInstance", "kind": "Gdef"}, "ToolCallValidMetricValue": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolCallValidMetricValue", "kind": "Gdef"}, "ToolCallValidResults": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolCallValidResults", "kind": "Gdef"}, "ToolCallValidSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolCallValidSpec", "kind": "Gdef"}, "ToolConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tool.ToolConfig", "kind": "Gdef"}, "ToolNameMatchInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolNameMatchInput", "kind": "Gdef"}, "ToolNameMatchInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolNameMatchInstance", "kind": "Gdef"}, "ToolNameMatchMetricValue": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolNameMatchMetricValue", "kind": "Gdef"}, "ToolNameMatchResults": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolNameMatchResults", "kind": "Gdef"}, "ToolNameMatchSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolNameMatchSpec", "kind": "Gdef"}, "ToolParameterKVMatchInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolParameterKVMatchInput", "kind": "Gdef"}, "ToolParameterKVMatchInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolParameterKVMatchInstance", "kind": "Gdef"}, "ToolParameterKVMatchMetricValue": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolParameterKVMatchMetricValue", "kind": "Gdef"}, "ToolParameterKVMatchResults": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolParameterKVMatchResults", "kind": "Gdef"}, "ToolParameterKVMatchSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolParameterKVMatchSpec", "kind": "Gdef"}, "ToolParameterKeyMatchInput": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolParameterKeyMatchInput", "kind": "Gdef"}, "ToolParameterKeyMatchInstance": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolParameterKeyMatchInstance", "kind": "Gdef"}, "ToolParameterKeyMatchMetricValue": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolParameterKeyMatchMetricValue", "kind": "Gdef"}, "ToolParameterKeyMatchResults": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolParameterKeyMatchResults", "kind": "Gdef"}, "ToolParameterKeyMatchSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.evaluation_service.ToolParameterKeyMatchSpec", "kind": "Gdef"}, "ToolUseExample": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tool.ToolUseExample", "kind": "Gdef"}, "TrainingConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.data_labeling_job.TrainingConfig", "kind": "Gdef"}, "TrainingPipeline": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.training_pipeline.TrainingPipeline", "kind": "Gdef"}, "Trial": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.study.Trial", "kind": "Gdef"}, "TrialContext": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.study.TrialContext", "kind": "Gdef"}, "TunedModel": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tuning_job.TunedModel", "kind": "Gdef"}, "TunedModelRef": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tuning_job.TunedModelRef", "kind": "Gdef"}, "TuningDataStats": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tuning_job.TuningDataStats", "kind": "Gdef"}, "TuningJob": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tuning_job.TuningJob", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.openapi.Type", "kind": "Gdef"}, "UndeployIndexOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.UndeployIndexOperationMetadata", "kind": "Gdef"}, "UndeployIndexRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.UndeployIndexRequest", "kind": "Gdef"}, "UndeployIndexResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.UndeployIndexResponse", "kind": "Gdef"}, "UndeployModelOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.UndeployModelOperationMetadata", "kind": "Gdef"}, "UndeployModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.UndeployModelRequest", "kind": "Gdef"}, "UndeployModelResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.UndeployModelResponse", "kind": "Gdef"}, "UnmanagedContainerModel": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.unmanaged_container_model.UnmanagedContainerModel", "kind": "Gdef"}, "UpdateArtifactRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.UpdateArtifactRequest", "kind": "Gdef"}, "UpdateCachedContentRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.gen_ai_cache_service.UpdateCachedContentRequest", "kind": "Gdef"}, "UpdateContextRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.UpdateContextRequest", "kind": "Gdef"}, "UpdateDatasetRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.UpdateDatasetRequest", "kind": "Gdef"}, "UpdateDatasetVersionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.dataset_service.UpdateDatasetVersionRequest", "kind": "Gdef"}, "UpdateDeploymentResourcePoolOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.deployment_resource_pool_service.UpdateDeploymentResourcePoolOperationMetadata", "kind": "Gdef"}, "UpdateDeploymentResourcePoolRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.deployment_resource_pool_service.UpdateDeploymentResourcePoolRequest", "kind": "Gdef"}, "UpdateEndpointRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.endpoint_service.UpdateEndpointRequest", "kind": "Gdef"}, "UpdateEntityTypeRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.UpdateEntityTypeRequest", "kind": "Gdef"}, "UpdateExecutionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.metadata_service.UpdateExecutionRequest", "kind": "Gdef"}, "UpdateExplanationDatasetOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.UpdateExplanationDatasetOperationMetadata", "kind": "Gdef"}, "UpdateExplanationDatasetRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.UpdateExplanationDatasetRequest", "kind": "Gdef"}, "UpdateExplanationDatasetResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.UpdateExplanationDatasetResponse", "kind": "Gdef"}, "UpdateExtensionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.extension_registry_service.UpdateExtensionRequest", "kind": "Gdef"}, "UpdateFeatureGroupOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_registry_service.UpdateFeatureGroupOperationMetadata", "kind": "Gdef"}, "UpdateFeatureGroupRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_registry_service.UpdateFeatureGroupRequest", "kind": "Gdef"}, "UpdateFeatureOnlineStoreOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.UpdateFeatureOnlineStoreOperationMetadata", "kind": "Gdef"}, "UpdateFeatureOnlineStoreRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.UpdateFeatureOnlineStoreRequest", "kind": "Gdef"}, "UpdateFeatureOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_registry_service.UpdateFeatureOperationMetadata", "kind": "Gdef"}, "UpdateFeatureRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.UpdateFeatureRequest", "kind": "Gdef"}, "UpdateFeatureViewOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.UpdateFeatureViewOperationMetadata", "kind": "Gdef"}, "UpdateFeatureViewRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.feature_online_store_admin_service.UpdateFeatureViewRequest", "kind": "Gdef"}, "UpdateFeaturestoreOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.UpdateFeaturestoreOperationMetadata", "kind": "Gdef"}, "UpdateFeaturestoreRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_service.UpdateFeaturestoreRequest", "kind": "Gdef"}, "UpdateIndexEndpointRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_endpoint_service.UpdateIndexEndpointRequest", "kind": "Gdef"}, "UpdateIndexOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_service.UpdateIndexOperationMetadata", "kind": "Gdef"}, "UpdateIndexRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_service.UpdateIndexRequest", "kind": "Gdef"}, "UpdateModelDeploymentMonitoringJobOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.UpdateModelDeploymentMonitoringJobOperationMetadata", "kind": "Gdef"}, "UpdateModelDeploymentMonitoringJobRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.job_service.UpdateModelDeploymentMonitoringJobRequest", "kind": "Gdef"}, "UpdateModelMonitorOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.UpdateModelMonitorOperationMetadata", "kind": "Gdef"}, "UpdateModelMonitorRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_monitoring_service.UpdateModelMonitorRequest", "kind": "Gdef"}, "UpdateModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.UpdateModelRequest", "kind": "Gdef"}, "UpdateNotebookRuntimeTemplateRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.UpdateNotebookRuntimeTemplateRequest", "kind": "Gdef"}, "UpdatePersistentResourceOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource_service.UpdatePersistentResourceOperationMetadata", "kind": "Gdef"}, "UpdatePersistentResourceRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.persistent_resource_service.UpdatePersistentResourceRequest", "kind": "Gdef"}, "UpdateRagCorpusOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.UpdateRagCorpusOperationMetadata", "kind": "Gdef"}, "UpdateRagCorpusRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.UpdateRagCorpusRequest", "kind": "Gdef"}, "UpdateReasoningEngineOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.reasoning_engine_service.UpdateReasoningEngineOperationMetadata", "kind": "Gdef"}, "UpdateReasoningEngineRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.reasoning_engine_service.UpdateReasoningEngineRequest", "kind": "Gdef"}, "UpdateScheduleRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.schedule_service.UpdateScheduleRequest", "kind": "Gdef"}, "UpdateSpecialistPoolOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.specialist_pool_service.UpdateSpecialistPoolOperationMetadata", "kind": "Gdef"}, "UpdateSpecialistPoolRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.specialist_pool_service.UpdateSpecialistPoolRequest", "kind": "Gdef"}, "UpdateTensorboardExperimentRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.UpdateTensorboardExperimentRequest", "kind": "Gdef"}, "UpdateTensorboardOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.UpdateTensorboardOperationMetadata", "kind": "Gdef"}, "UpdateTensorboardRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.UpdateTensorboardRequest", "kind": "Gdef"}, "UpdateTensorboardRunRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.UpdateTensorboardRunRequest", "kind": "Gdef"}, "UpdateTensorboardTimeSeriesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.UpdateTensorboardTimeSeriesRequest", "kind": "Gdef"}, "UpgradeNotebookRuntimeOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.UpgradeNotebookRuntimeOperationMetadata", "kind": "Gdef"}, "UpgradeNotebookRuntimeRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.UpgradeNotebookRuntimeRequest", "kind": "Gdef"}, "UpgradeNotebookRuntimeResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.notebook_service.UpgradeNotebookRuntimeResponse", "kind": "Gdef"}, "UploadModelOperationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.UploadModelOperationMetadata", "kind": "Gdef"}, "UploadModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.UploadModelRequest", "kind": "Gdef"}, "UploadModelResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.model_service.UploadModelResponse", "kind": "Gdef"}, "UploadRagFileConfig": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data.UploadRagFileConfig", "kind": "Gdef"}, "UploadRagFileRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.UploadRagFileRequest", "kind": "Gdef"}, "UploadRagFileResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.vertex_rag_data_service.UploadRagFileResponse", "kind": "Gdef"}, "UpsertDatapointsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_service.UpsertDatapointsRequest", "kind": "Gdef"}, "UpsertDatapointsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.index_service.UpsertDatapointsResponse", "kind": "Gdef"}, "UserActionReference": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.user_action_reference.UserActionReference", "kind": "Gdef"}, "Value": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.value.Value", "kind": "Gdef"}, "VertexAISearch": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tool.VertexAISearch", "kind": "Gdef"}, "VertexRagDataServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.async_client.VertexRagDataServiceAsyncClient", "kind": "Gdef"}, "VertexRagDataServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.vertex_rag_data_service.client.VertexRagDataServiceClient", "kind": "Gdef"}, "VertexRagServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.vertex_rag_service.async_client.VertexRagServiceAsyncClient", "kind": "Gdef"}, "VertexRagServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.vertex_rag_service.client.VertexRagServiceClient", "kind": "Gdef"}, "VertexRagStore": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tool.VertexRagStore", "kind": "Gdef"}, "VideoMetadata": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.content.VideoMetadata", "kind": "Gdef"}, "VizierServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.vizier_service.async_client.VizierServiceAsyncClient", "kind": "Gdef"}, "VizierServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.services.vizier_service.client.VizierServiceClient", "kind": "Gdef"}, "WorkerPoolSpec": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.custom_job.WorkerPoolSpec", "kind": "Gdef"}, "WriteFeatureValuesPayload": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_online_service.WriteFeatureValuesPayload", "kind": "Gdef"}, "WriteFeatureValuesRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_online_service.WriteFeatureValuesRequest", "kind": "Gdef"}, "WriteFeatureValuesResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.featurestore_online_service.WriteFeatureValuesResponse", "kind": "Gdef"}, "WriteTensorboardExperimentDataRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.WriteTensorboardExperimentDataRequest", "kind": "Gdef"}, "WriteTensorboardExperimentDataResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.WriteTensorboardExperimentDataResponse", "kind": "Gdef"}, "WriteTensorboardRunDataRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.WriteTensorboardRunDataRequest", "kind": "Gdef"}, "WriteTensorboardRunDataResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.tensorboard_service.WriteTensorboardRunDataResponse", "kind": "Gdef"}, "XraiAttribution": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.types.explanation.XraiAttribution", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.aiplatform_v1beta1.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.aiplatform_v1beta1.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "package_version": {".class": "SymbolTableNode", "cross_ref": "google.cloud.aiplatform_v1beta1.gapic_version", "kind": "Gdef", "module_public": false}}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/cloud/aiplatform_v1beta1/__init__.py"}