{"data_mtime": 1755541222, "dep_lines": [21, 20, 25, 31, 18, 19, 20, 31, 18, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 28], "dep_prios": [5, 10, 5, 10, 10, 10, 20, 20, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["google.api_core.operations_v1.transports.base", "google.api_core.operations_v1.pagers_async", "google.api_core.operations_v1.abstract_operations_base_client", "google.auth.aio.credentials", "google.api_core.client_options", "google.api_core.gapic_v1", "google.api_core.operations_v1", "google.auth.aio", "google.api_core", "typing", "builtins", "_frozen_importlib", "abc", "enum", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.gapic_v1.method_async", "google.api_core.gapic_v1.routing_header", "google.api_core.operations_v1.pagers_base", "google.api_core.operations_v1.transports", "google.auth", "google.auth._credentials_base"], "hash": "71922b5fdebe577d41e079e24cc89a7827058382", "id": "google.api_core.operations_v1.operations_rest_client_async", "ignore_all": true, "interface_hash": "395f40dbd520a436eb4e4bd1f62b31be64f04cc5", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/api_core/operations_v1/operations_rest_client_async.py", "plugin_data": null, "size": 14616, "suppressed": ["google.longrunning"], "version_id": "1.17.1"}