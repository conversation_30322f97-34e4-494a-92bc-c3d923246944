{".class": "MypyFile", "_fullname": "google.api_core.operations_v1.abstract_operations_base_client", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"google.api_core.client_options.ClientOptions\" and \"builtins.dict[Any, Any]\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.api_core.client_options.ClientOptions", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.operations_v1.abstract_operations_base_client.<subclass of \"google.api_core.client_options.ClientOptions\" and \"builtins.dict[Any, Any]\">", "name": "<subclass of \"google.api_core.client_options.ClientOptions\" and \"builtins.dict[Any, Any]\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.<subclass of \"google.api_core.client_options.ClientOptions\" and \"builtins.dict[Any, Any]\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.api_core.operations_v1.abstract_operations_base_client", "mro": ["google.api_core.operations_v1.abstract_operations_base_client.<subclass of \"google.api_core.client_options.ClientOptions\" and \"builtins.dict[Any, Any]\">", "google.api_core.client_options.ClientOptions", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ASYNC_REST_EXCEPTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.ASYNC_REST_EXCEPTION", "name": "ASYNC_REST_EXCEPTION", "setter_type": null, "type": "builtins.ImportError"}}, "AbstractOperationsBaseClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClientMeta", "defn": {".class": "ClassDef", "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient", "name": "AbstractOperationsBaseClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient", "has_param_spec_type": false, "metaclass_type": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClientMeta", "metadata": {}, "module_name": "google.api_core.operations_v1.abstract_operations_base_client", "mro": ["google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_ENDPOINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.DEFAULT_ENDPOINT", "name": "DEFAULT_ENDPOINT", "setter_type": null, "type": "builtins.str"}}, "DEFAULT_MTLS_ENDPOINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.DEFAULT_MTLS_ENDPOINT", "name": "DEFAULT_MTLS_ENDPOINT", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "credentials", "transport", "client_options", "client_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "credentials", "transport", "client_options", "client_info"], "arg_types": ["google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient", {".class": "UnionType", "items": ["google.auth.credentials.Credentials", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "google.api_core.operations_v1.transports.base.OperationsTransport", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.api_core.client_options.ClientOptions", {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.api_core.gapic_v1.client_info.ClientInfo"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AbstractOperationsBaseClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_default_mtls_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["api_endpoint"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient._get_default_mtls_endpoint", "name": "_get_default_mtls_endpoint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient._get_default_mtls_endpoint", "name": "_get_default_mtls_endpoint", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["api_endpoint"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_default_mtls_endpoint of AbstractOperationsBaseClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_transport": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient._transport", "name": "_transport", "setter_type": null, "type": "google.api_core.operations_v1.transports.base.OperationsTransport"}}, "common_billing_account_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["billing_account"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.common_billing_account_path", "name": "common_billing_account_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["billing_account"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "common_billing_account_path of AbstractOperationsBaseClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.common_billing_account_path", "name": "common_billing_account_path", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["billing_account"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "common_billing_account_path of AbstractOperationsBaseClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "common_folder_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["folder"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.common_folder_path", "name": "common_folder_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["folder"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "common_folder_path of AbstractOperationsBaseClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.common_folder_path", "name": "common_folder_path", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["folder"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "common_folder_path of AbstractOperationsBaseClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "common_location_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["project", "location"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.common_location_path", "name": "common_location_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["project", "location"], "arg_types": ["builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "common_location_path of AbstractOperationsBaseClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.common_location_path", "name": "common_location_path", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["project", "location"], "arg_types": ["builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "common_location_path of AbstractOperationsBaseClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "common_organization_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["organization"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.common_organization_path", "name": "common_organization_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["organization"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "common_organization_path of AbstractOperationsBaseClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.common_organization_path", "name": "common_organization_path", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["organization"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "common_organization_path of AbstractOperationsBaseClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "common_project_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["project"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.common_project_path", "name": "common_project_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["project"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "common_project_path of AbstractOperationsBaseClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.common_project_path", "name": "common_project_path", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["project"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "common_project_path of AbstractOperationsBaseClient", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_service_account_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "filename", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.from_service_account_file", "name": "from_service_account_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "filename", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_service_account_file of AbstractOperationsBaseClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.from_service_account_file", "name": "from_service_account_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "filename", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_service_account_file of AbstractOperationsBaseClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_service_account_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "info", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.from_service_account_info", "name": "from_service_account_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "info", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_service_account_info of AbstractOperationsBaseClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.from_service_account_info", "name": "from_service_account_info", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "info", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_service_account_info of AbstractOperationsBaseClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_service_account_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.from_service_account_json", "name": "from_service_account_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "filename", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_common_billing_account_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.parse_common_billing_account_path", "name": "parse_common_billing_account_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_common_billing_account_path of AbstractOperationsBaseClient", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.parse_common_billing_account_path", "name": "parse_common_billing_account_path", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_common_billing_account_path of AbstractOperationsBaseClient", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parse_common_folder_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.parse_common_folder_path", "name": "parse_common_folder_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_common_folder_path of AbstractOperationsBaseClient", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.parse_common_folder_path", "name": "parse_common_folder_path", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_common_folder_path of AbstractOperationsBaseClient", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parse_common_location_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.parse_common_location_path", "name": "parse_common_location_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_common_location_path of AbstractOperationsBaseClient", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.parse_common_location_path", "name": "parse_common_location_path", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_common_location_path of AbstractOperationsBaseClient", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parse_common_organization_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.parse_common_organization_path", "name": "parse_common_organization_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_common_organization_path of AbstractOperationsBaseClient", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.parse_common_organization_path", "name": "parse_common_organization_path", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_common_organization_path of AbstractOperationsBaseClient", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parse_common_project_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.parse_common_project_path", "name": "parse_common_project_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_common_project_path of AbstractOperationsBaseClient", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.parse_common_project_path", "name": "parse_common_project_path", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_common_project_path of AbstractOperationsBaseClient", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.transport", "name": "transport", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transport of AbstractOperationsBaseClient", "ret_type": "google.api_core.operations_v1.transports.base.OperationsTransport", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.transport", "name": "transport", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transport of AbstractOperationsBaseClient", "ret_type": "google.api_core.operations_v1.transports.base.OperationsTransport", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractOperationsBaseClientMeta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClientMeta", "name": "AbstractOperationsBaseClientMeta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClientMeta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.api_core.operations_v1.abstract_operations_base_client", "mro": ["google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClientMeta", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "_transport_registry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClientMeta._transport_registry", "name": "_transport_registry", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "google.api_core.operations_v1.transports.base.OperationsTransport"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_transport_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["cls", "label"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClientMeta.get_transport_class", "name": "get_transport_class", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "label"], "arg_types": ["google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClientMeta", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_transport_class of AbstractOperationsBaseClientMeta", "ret_type": {".class": "TypeType", "item": "google.api_core.operations_v1.transports.base.OperationsTransport"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClientMeta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.api_core.operations_v1.abstract_operations_base_client.AbstractOperationsBaseClientMeta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncOperationsRestTransport": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1.transports.rest_asyncio.AsyncOperationsRestTransport", "kind": "Gdef"}, "DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1.transports.base.DEFAULT_CLIENT_INFO", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "HAS_ASYNC_REST_DEPENDENCIES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.HAS_ASYNC_REST_DEPENDENCIES", "name": "HAS_ASYNC_REST_DEPENDENCIES", "setter_type": null, "type": "builtins.bool"}}, "MutualTLSChannelError": {".class": "SymbolTableNode", "cross_ref": "google.auth.exceptions.MutualTLSChannelError", "kind": "Gdef"}, "OperationsRestTransport": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1.transports.rest.OperationsRestTransport", "kind": "Gdef"}, "OperationsTransport": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operations_v1.transports.base.OperationsTransport", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "client_options_lib": {".class": "SymbolTableNode", "cross_ref": "google.api_core.client_options", "kind": "Gdef"}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.operations_v1.abstract_operations_base_client.e", "name": "e", "setter_type": null, "type": {".class": "DeletedType", "source": "e"}}}, "ga_credentials": {".class": "SymbolTableNode", "cross_ref": "google.auth.credentials", "kind": "Gdef"}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef"}, "mtls": {".class": "SymbolTableNode", "cross_ref": "google.auth.transport.mtls", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/api_core/operations_v1/abstract_operations_base_client.py"}