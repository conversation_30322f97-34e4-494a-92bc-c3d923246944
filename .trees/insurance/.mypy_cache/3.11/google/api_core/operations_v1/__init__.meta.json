{"data_mtime": 1755541222, "dep_lines": [20, 30, 17, 18, 19, 33, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["google.api_core.operations_v1.transports.rest", "google.api_core.operations_v1.transports.rest_asyncio", "google.api_core.operations_v1.abstract_operations_client", "google.api_core.operations_v1.operations_async_client", "google.api_core.operations_v1.operations_client", "google.api_core.operations_v1.operations_rest_client_async", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "99a304f7846bfd845f47f995d5d0d1d4ec1ec7ec", "id": "google.api_core.operations_v1", "ignore_all": true, "interface_hash": "4e2aa56c871673179b5af0cc6a6671377adf19eb", "mtime": 1753678816, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/api_core/operations_v1/__init__.py", "plugin_data": null, "size": 1638, "suppressed": [], "version_id": "1.17.1"}