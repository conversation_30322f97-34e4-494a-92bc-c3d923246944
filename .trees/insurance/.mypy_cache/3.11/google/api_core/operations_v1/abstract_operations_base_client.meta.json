{"data_mtime": 1755541222, "dep_lines": [23, 27, 30, 41, 21, 22, 39, 40, 41, 21, 39, 16, 17, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 5, 20, 20, 20, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["google.api_core.operations_v1.transports.base", "google.api_core.operations_v1.transports.rest", "google.api_core.operations_v1.transports.rest_asyncio", "google.auth.transport.mtls", "google.api_core.client_options", "google.api_core.gapic_v1", "google.auth.credentials", "google.auth.exceptions", "google.auth.transport", "google.api_core", "google.auth", "collections", "os", "re", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.operations_v1.transports", "google.auth._credentials_base", "google.auth.aio", "google.auth.aio.credentials", "types"], "hash": "c06e47295ffe2d1b79f2207fc3cc24b06e14f626", "id": "google.api_core.operations_v1.abstract_operations_base_client", "ignore_all": true, "interface_hash": "d62bd616ccbf62d48c264e25ffe19cc6e999aaa7", "mtime": 1753678816, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/google/api_core/operations_v1/abstract_operations_base_client.py", "plugin_data": null, "size": 14861, "suppressed": [], "version_id": "1.17.1"}