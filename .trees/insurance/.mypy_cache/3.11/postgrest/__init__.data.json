{".class": "MypyFile", "_fullname": "postgrest", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIError": {".class": "SymbolTableNode", "cross_ref": "postgrest.exceptions.APIError", "kind": "Gdef"}, "APIResponse": {".class": "SymbolTableNode", "cross_ref": "postgrest.base_request_builder.APIResponse", "kind": "Gdef"}, "AsyncFilterRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._async.request_builder.AsyncFilterRequestBuilder", "kind": "Gdef"}, "AsyncMaybeSingleRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._async.request_builder.AsyncMaybeSingleRequestBuilder", "kind": "Gdef"}, "AsyncPostgrestClient": {".class": "SymbolTableNode", "cross_ref": "postgrest._async.client.AsyncPostgrestClient", "kind": "Gdef"}, "AsyncQueryRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._async.request_builder.AsyncQueryRequestBuilder", "kind": "Gdef"}, "AsyncRPCFilterRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._async.request_builder.AsyncRPCFilterRequestBuilder", "kind": "Gdef"}, "AsyncRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._async.request_builder.AsyncRequestBuilder", "kind": "Gdef"}, "AsyncSelectRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._async.request_builder.AsyncSelectRequestBuilder", "kind": "Gdef"}, "AsyncSingleRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._async.request_builder.AsyncSingleRequestBuilder", "kind": "Gdef"}, "CountMethod": {".class": "SymbolTableNode", "cross_ref": "postgrest.types.CountMethod", "kind": "Gdef"}, "DEFAULT_POSTGREST_CLIENT_HEADERS": {".class": "SymbolTableNode", "cross_ref": "postgrest.constants.DEFAULT_POSTGREST_CLIENT_HEADERS", "kind": "Gdef"}, "Filters": {".class": "SymbolTableNode", "cross_ref": "postgrest.types.Filters", "kind": "Gdef"}, "RequestMethod": {".class": "SymbolTableNode", "cross_ref": "postgrest.types.RequestMethod", "kind": "Gdef"}, "ReturnMethod": {".class": "SymbolTableNode", "cross_ref": "postgrest.types.ReturnMethod", "kind": "Gdef"}, "SyncFilterRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._sync.request_builder.SyncFilterRequestBuilder", "kind": "Gdef"}, "SyncMaybeSingleRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._sync.request_builder.SyncMaybeSingleRequestBuilder", "kind": "Gdef"}, "SyncPostgrestClient": {".class": "SymbolTableNode", "cross_ref": "postgrest._sync.client.SyncPostgrestClient", "kind": "Gdef"}, "SyncQueryRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._sync.request_builder.SyncQueryRequestBuilder", "kind": "Gdef"}, "SyncRPCFilterRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._sync.request_builder.SyncRPCFilterRequestBuilder", "kind": "Gdef"}, "SyncRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._sync.request_builder.SyncRequestBuilder", "kind": "Gdef"}, "SyncSelectRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._sync.request_builder.SyncSelectRequestBuilder", "kind": "Gdef"}, "SyncSingleRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._sync.request_builder.SyncSingleRequestBuilder", "kind": "Gdef"}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "httpx._config.Timeout", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "postgrest.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "postgrest.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "postgrest.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "postgrest.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "postgrest.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "postgrest.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "postgrest.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "postgrest.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "postgrest.version.__version__", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}}, "path": "/opt/homebrew/lib/python3.12/site-packages/postgrest/__init__.py"}