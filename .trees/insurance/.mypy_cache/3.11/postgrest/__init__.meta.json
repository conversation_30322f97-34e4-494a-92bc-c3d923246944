{"data_mtime": 1755541222, "dep_lines": [5, 6, 15, 16, 25, 26, 27, 28, 34, 1, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["postgrest._async.client", "postgrest._async.request_builder", "postgrest._sync.client", "postgrest._sync.request_builder", "postgrest.base_request_builder", "postgrest.constants", "postgrest.exceptions", "postgrest.types", "postgrest.version", "__future__", "httpx", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "7c11b0fc962bfeccd4a5848cd64035a3f6256ebb", "id": "postgrest", "ignore_all": true, "interface_hash": "d3cbf5bcf84806577f9a496c8d010411e5bdcffd", "mtime": 1753678819, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/opt/homebrew/lib/python3.12/site-packages/postgrest/__init__.py", "plugin_data": null, "size": 1634, "suppressed": [], "version_id": "1.17.1"}