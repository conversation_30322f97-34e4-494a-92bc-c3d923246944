{".class": "MypyFile", "_fullname": "postgrest._sync.client", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BasePostgrestClient": {".class": "SymbolTableNode", "cross_ref": "postgrest.base_client.BasePostgrestClient", "kind": "Gdef"}, "Client": {".class": "SymbolTableNode", "cross_ref": "httpx._client.Client", "kind": "Gdef"}, "CountMethod": {".class": "SymbolTableNode", "cross_ref": "postgrest.types.CountMethod", "kind": "Gdef"}, "DEFAULT_POSTGREST_CLIENT_HEADERS": {".class": "SymbolTableNode", "cross_ref": "postgrest.constants.DEFAULT_POSTGREST_CLIENT_HEADERS", "kind": "Gdef"}, "DEFAULT_POSTGREST_CLIENT_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "postgrest.constants.DEFAULT_POSTGREST_CLIENT_TIMEOUT", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Headers": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Headers", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "QueryParams": {".class": "SymbolTableNode", "cross_ref": "httpx._urls.QueryParams", "kind": "Gdef"}, "SyncPostgrestClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["postgrest.base_client.BasePostgrestClient"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "postgrest._sync.client.SyncPostgrestClient", "name": "SyncPostgrestClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "postgrest._sync.client.SyncPostgrestClient", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "postgrest._sync.client", "mro": ["postgrest._sync.client.SyncPostgrestClient", "postgrest.base_client.BasePostgrestClient", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "postgrest._sync.client.SyncPostgrestClient.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["postgrest._sync.client.SyncPostgrestClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of SyncPostgrestClient", "ret_type": "postgrest._sync.client.SyncPostgrestClient", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "postgrest._sync.client.SyncPostgrestClient.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["postgrest._sync.client.SyncPostgrestClient", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of SyncPostgrestClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "base_url", "schema", "headers", "timeout", "verify", "proxy", "http_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "postgrest._sync.client.SyncPostgrestClient.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "base_url", "schema", "headers", "timeout", "verify", "proxy", "http_client"], "arg_types": ["postgrest._sync.client.SyncPostgrestClient", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "httpx._config.Timeout", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["httpx._client.Client", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SyncPostgrestClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aclose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "postgrest._sync.client.SyncPostgrestClient.aclose", "name": "aclose", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["postgrest._sync.client.SyncPostgrestClient"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aclose of SyncPostgrestClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "base_url", "headers", "timeout", "verify", "proxy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "postgrest._sync.client.SyncPostgrestClient.create_session", "name": "create_session", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "base_url", "headers", "timeout", "verify", "proxy"], "arg_types": ["postgrest._sync.client.SyncPostgrestClient", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "httpx._config.Timeout"], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_session of SyncPostgrestClient", "ret_type": "httpx._client.Client", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "postgrest._sync.client.SyncPostgrestClient.from_", "name": "from_", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "table"], "arg_types": ["postgrest._sync.client.SyncPostgrestClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_ of SyncPostgrestClient", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "postgrest._sync.client._TableT"}], "extra_attrs": null, "type_ref": "postgrest._sync.request_builder.SyncRequestBuilder"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "postgrest._sync.client.SyncPostgrestClient.from_table", "name": "from_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "table"], "arg_types": ["postgrest._sync.client.SyncPostgrestClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_table of SyncPostgrestClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "postgrest._sync.request_builder.SyncRequestBuilder"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "postgrest._sync.client.SyncPostgrestClient.from_table", "name": "from_table", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "postgrest._sync.client.deprecated", "source_any": {".class": "AnyType", "missing_import_name": "postgrest._sync.client.deprecated", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "rpc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "func", "params", "count", "head", "get"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "postgrest._sync.client.SyncPostgrestClient.rpc", "name": "rpc", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "func", "params", "count", "head", "get"], "arg_types": ["postgrest._sync.client.SyncPostgrestClient", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["postgrest.types.CountMethod", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "rpc of SyncPostgrestClient", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "postgrest._sync.request_builder.SyncRPCFilterRequestBuilder"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "postgrest._sync.client.SyncPostgrestClient.schema", "name": "schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["postgrest._sync.client.SyncPostgrestClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "schema of SyncPostgrestClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "postgrest._sync.client.SyncPostgrestClient.table", "name": "table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "table"], "arg_types": ["postgrest._sync.client.SyncPostgrestClient", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "table of SyncPostgrestClient", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "postgrest._sync.client._TableT"}], "extra_attrs": null, "type_ref": "postgrest._sync.request_builder.SyncRequestBuilder"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "postgrest._sync.client.SyncPostgrestClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "postgrest._sync.client.SyncPostgrestClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SyncRPCFilterRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._sync.request_builder.SyncRPCFilterRequestBuilder", "kind": "Gdef"}, "SyncRequestBuilder": {".class": "SymbolTableNode", "cross_ref": "postgrest._sync.request_builder.SyncRequestBuilder", "kind": "Gdef"}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "httpx._config.Timeout", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_TableT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "postgrest._sync.client._TableT", "line": 18, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "postgrest._sync.client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "postgrest._sync.client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "postgrest._sync.client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "postgrest._sync.client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "postgrest._sync.client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "postgrest._sync.client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "postgrest.version.__version__", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "deprecated": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "postgrest._sync.client.deprecated", "name": "deprecated", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "postgrest._sync.client.deprecated", "source_any": null, "type_of_any": 3}}}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}}, "path": "/opt/homebrew/lib/python3.12/site-packages/postgrest/_sync/client.py"}